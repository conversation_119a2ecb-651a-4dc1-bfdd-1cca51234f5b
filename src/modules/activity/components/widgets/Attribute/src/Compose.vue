<template>
  <div class="compose">
    <!-- eslint-disable vue/html-self-closing -->
    <!-- <div v-html="showText"></div> -->
    <a-form-model
      v-if="show"
      ref="form"
      class="attribute-form"
      :model="form"
      label-position="right"
      layout="horizontal"
      label-align="left"
      :label-col="{
        span: 7
      }"
      :wrapper-col="{
        span: 23,
        offset: 1
      }"
      @submit.native.prevent
    >
      <template v-if="chosen">
        <div v-for="variable in ui_var_map" :key="variable.key" style="margin-top: 6px">
          <component
            :is="getComponentName(option, variable)"
            :ref_data="ref_data[variable.key]"
            :display_html.sync="label_html[variable.key]"
            :form.sync="form[variable.key]"
            :chosen="chosen"
            :disabled="!chosen || readonly || getAIDisabled(variable.type)"
            :row="variable"
            :values="variable.values"
            :num_type="variable.type"
            :type="key_type[variable.type]"
            :var_map="variable"
            :config="option.config"
            :extra-config="$attrs.config"
            :option="option"
            :is-activate-editing="isActivateEditing"
            :render="renderComponentOnRefTag[option.ref_field_tag] || null"
            :data_var_map="currentData[variable.key]"
            v-bind="$attrs"
            :word-count-limit="wordCountLimit"
            :show-suffix="showSuffix"
            v-on="$listeners"
            @change="(val) => handleChangeVal(val, variable)"
            @changeStructSelect="(data) => handleStructSelectChange(data, option)"
            @changeStructInput="(data) => handleStructInputChange(data, option)"
          />
        </div>
      </template>
    </a-form-model>
  </div>
</template>

<script>
import {
  StructLink,
  StructInput,
  StructSelect,
  StructDatepicker,
  StructGlobalpicker,
  StructPhoto
} from '../components/index'
import RenderComponent from './RenderComponent'

let typeStruct = ADMIN_API.struct.dict([
  'string',
  ADMIN_API.struct.interface({
    attr_item_id: 'number',
    type: 'number?', // FIXME
    key: 'string',
    values: ADMIN_API.struct.union([
      'null|undefined',
      [
        ADMIN_API.struct.interface({
          create: 'boolean?', // create is mandatory , this is required when post
          id: 'number?', // omit when create,
          type: 'number?', // for backend to know if need translate, this is required when post
          value: 'string?'
        })
      ]
    ])
  })
])

const basic_info_attribute_mixin = {
  props: {
    renderComponentOnRefTag: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getComponentName(option, variable) {
      if (
        option.ref_field_tag in this.renderComponentOnRefTag &&
        typeof this.renderComponentOnRefTag[option.ref_field_tag] === 'function'
      ) {
        return 'render-component'
      }

      return this.cpnt_type[this.key_type[variable.type]]
    },
    handleStructInputChange({ value, ref_field_tag }, option) {
      const linkageRelation = [
        {
          option_ref_field_tag: 'valid_time_options_a',
          from: 'attr_item_freetext_2',
          to: ['attr_item_freetext_2']
        }
      ]
      let hasChange = false

      linkageRelation.forEach((linkage) => {
        if (option.ref_field_tag === linkage.option_ref_field_tag && ref_field_tag === linkage.from) {
          this.ui_var_map.forEach((variable) => {
            if (linkage.to.includes(variable.ref_field_tag)) {
              hasChange = true
              // 基本信息的属性需要特殊的联动
              this.$set(this.currentData, variable.key, {
                ...this.currentData[variable.key],
                values: value
              })
            }
          })
        }
      })

      hasChange && this.$emit('change', this.currentData)
    },
    // 下拉联动
    handleStructSelectChange({ index, ref_field_tag }, option) {
      const linkageRelation = [
        {
          option_ref_field_tag: 'valid_time_options_a',
          from: 'attr_item_specific_date',
          to: ['attr_item_specific_time', 'attr_item_specific_time_same_time']
        }
      ]

      if (-1 !== index) {
        linkageRelation.forEach((linkage) => {
          if (option.ref_field_tag === linkage.option_ref_field_tag && ref_field_tag === linkage.from) {
            this.ui_var_map.forEach((variable) => {
              if (linkage.to.includes(variable.ref_field_tag)) {
                // 基本信息的属性需要特殊的联动
                this.$set(this.currentData, variable.key, {
                  ...this.currentData[variable.key],
                  values: [variable.values[index]]
                })
              }
            })
          }
        })

        this.$emit('change', this.currentData)
      }
    }
  }
}

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Compose',
  components: {
    RenderComponent,
    StructLink,
    StructInput,
    StructSelect,
    StructDatepicker,
    StructGlobalpicker,
    StructPhoto
  },
  mixins: [basic_info_attribute_mixin],
  inject: ['updateText'],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  props: {
    ref_data: Object,
    ui_var_map: { type: Array },
    data_var_map: { type: Object },
    // 校验
    label: {
      type: String,
      default: ''
    },
    // disabled
    chosen: {
      type: Boolean,
      default: false
    },
    isActivateEditing: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    option: {
      type: Object,
      default: () => ({})
    },
    isPublishWithAi: {
      type: Boolean,
      default: false
    },
    // eslint-disable-next-line vue/prop-name-casing
    label_html: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // this form is only used for validation
      form: {},
      // label_html: {},
      show: true,
      key_type: {
        1: 'select',
        2: 'input',
        3: 'link',
        4: 'date',
        5: 'datetime',
        6: 'country',
        7: 'city',
        26: 'input',
        27: 'photo'
      },
      cpnt_type: {
        input: 'StructInput',
        select: 'StructSelect',
        datetime: 'StructDatepicker',
        date: 'StructDatepicker',
        link: 'StructLink',
        country: 'StructGlobalpicker',
        city: 'StructGlobalpicker',
        photo: 'StructPhoto'
      },
      commonmark: null
      // currentData: this.data_var_map
    }
  },
  computed: {
    currentData: {
      get() {
        return this.data_var_map
      }
    },
    showSuffix() {
      return this.wordCountLimit.length > 0
    },
    wordCountLimit() {
      return []
      // return this.$attrs?.config?.word_count_limit ?? []
    }
    // showText() {
    //   let template = this.label
    //   _.forEach(this.ui_var_map, (v) => {
    //     let isSelect = [1, 4, 5, 6, 7].includes(+v.type)
    //     let className = isSelect ? 'variable-select' : 'variable-input'
    //     let value = this.label_html[v.key]

    //     let el = `<span class="variable ${className} ${value ? 'variable-complete' : 'variable-undone'}"> ${
    //       +v.type === 2 && value ? this.fmtMd(value, true) : value || v.key
    //     } </span>` // 2: input
    //     // if (this.key_type[v.type] === "link") {
    //     //   el = `<a class="variable is-link" href=${
    //     //     val.linkUrl
    //     //   } target='_blank'> ${val.linkName} </a>`;
    //     // }
    //     template = template.replace(v.key, el)
    //   })
    //   return template
    // }
  },
  watch: {
    showText: {
      immediate: true,
      handler(next) {
        let obj = {
          label: this.label,
          data_var_map: {},
          _html: next
        }
        // for (let key in this.data_var_map) {\
        // (this.data_var_map[v.key].values || [])
        this.ui_var_map.forEach((v) => {
          obj.data_var_map[v.key] = {
            type: v.type,
            value: (_.get(this.data_var_map, `${v.key}.values`) || []).map((v) => v.value)
          }
        })
        // }
        this.updateText(this.option, obj)
      }
    },
    currentData: {
      deep: true,
      handler(v) {
        typeStruct.assert(_.cloneDeep(v))
        // need to add type back to backend
        Object.entries(v).forEach(([k, obj]) => {
          let currentVar = this.ui_var_map.find((ui_var) => ui_var.key == k)
          obj.type = (currentVar && currentVar.type) || 1
        })
      }
    },
    chosen() {
      this.$refs.form.resetFields()
    }
  },
  methods: {
    getAIDisabled(type) {
      const types = [2, 3]
      if (types.includes(type)) {
        return this.isPublishWithAi
      }
      return false
    },
    handleChangeVal(value, variable) {
      this.$set(this.currentData, [variable.key], value)
    },
    async validate() {
      let valid = await new Promise((resolve) => {
        this.$refs.form.validate((valid) => (valid ? resolve(true) : resolve(false)))
      })
      return valid
    }
  }
}
</script>

<style lang="scss" scoped>
.compose ::v-deep {
  .ant-input + .ant-input,
  textareaant-input + textarea.ant-input,
  .ant-input + textarea.ant-input {
    margin-top: 10px;
  }
  .attribute-form {
    .ant-form-item-label label {
      display: inline-flex;
      white-space: break-spaces;
      line-height: 1em;
    }
    .ant-input,
    .ant-select {
      width: inherit;
    }

    .ant-row.ant-form-item {
      min-height: auto;
    }
  }
  p {
    .is-link {
      text-decoration: underline;
    }
  }
  .ant-form {
    margin-bottom: 4px;
    .ant-form-item {
      margin-bottom: 0;
      label.ant-form-item-label {
        padding-top: 18px;
        font-weight: 400;
        line-height: 1.2;
      }
    }
    .ant-select,
    textarea.a-input,
    .a-input {
      max-width: 400px;
      margin-top: 10px;
      display: block;
    }
  }
  .disabled .ant-form-explain {
    display: none;
  }
  .ant-form-item.is-required.disabled .ant-form-item-label:before {
    display: none;
  }
}
</style>
