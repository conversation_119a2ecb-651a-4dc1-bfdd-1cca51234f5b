<template>
  <div class="guidance-video-management">
    <div v-if="isLoading" class="loading-container">
      <a-spin size="large" tip="Loading data..." />
    </div>

    <div v-else-if="!isLoading && sections.length === 0" class="empty-container">
      <a-empty description="No guidance video sections found." />
    </div>

    <div v-else class="main-content">
      <div class="section-list-container">
        <div class="section-list">
          <div
            v-for="section in sections"
            :id="section.section_key"
            :key="section.section_key"
            class="section-item"
          >
            <div
              class="section-header"
              :class="{ active: activeSectionKey === section.section_key }"
              @click="toggleSection(section.section_key)"
            >
              <span class="section-title">{{ section.section }} - {{ section.section_key }}</span>
              <a-icon :type="activeSectionKey === section.section_key ? 'up' : 'down'" />
            </div>
            <a-spin v-if="activeSectionKey === section.section_key" :spinning="isSaving" :delay="300">
              <a-collapse>
                <div class="section-content">
                  <a-form-model ref="form" :model="section" layout="vertical" class="section-form">
                    <a-form-model-item label="Title" prop="title" :required="false">
                      <a-input v-model="section.title" placeholder="Enter title's Text ID" />
                    </a-form-model-item>
                    <a-form-model-item label="Description" prop="desc" :required="false">
                      <a-input v-model="section.desc" placeholder="Enter Description's Text ID" />
                    </a-form-model-item>

                    <a-form-model-item label="Videos" prop="videos" :rules="[{ validator: validatorVideos }]">
                      <div class="language-grid">
                        <div v-for="lang in languages" :key="lang.B_LANG" class="language-item">
                          <div class="language-title">{{ lang.data }}&nbsp;&nbsp;&nbsp;{{ lang.name }}</div>
                          <div class="language-content">
                            <div
                              v-if="
                                getLangVideo(section.section_key, lang.data) &&
                                getLangVideo(section.section_key, lang.data).video_url
                              "
                              class="video-info"
                            >
                              <p class="video-url-container">
                                <a
                                  :title="getLangVideo(section.section_key, lang.data).video_url"
                                  @click="playVideo(getLangVideo(section.section_key, lang.data).video_url)"
                                >
                                  {{ getLangVideo(section.section_key, lang.data).video_url | truncateUrl }}
                                </a>
                                <a-icon
                                  type="delete"
                                  class="delete-icon"
                                  @click="removeVideo(section.section_key, lang.data)"
                                />
                              </p>
                              <a-button
                                type="dashed"
                                icon="upload"
                                size="small"
                                @click="handleReupload(section.section_key, lang.data)"
                              >
                                {{ $t('83893') }}
                              </a-button>
                            </div>
                            <div v-else>
                              <a-button
                                type="primary"
                                icon="upload"
                                size="small"
                                :loading="
                                  isUploading &&
                                  uploadingInfo.sectionKey === section.section_key &&
                                  uploadingInfo.langCode === lang.data
                                "
                                @click="handleUpload(section.section_key, lang.data)"
                              >
                                {{ $t('83892') }}
                              </a-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </a-form-model-item>
                  </a-form-model>

                  <div class="section-actions">
                    <a-button type="link" :disabled="isSaving" @click="handleReset(section)">
                      {{ $t('global_reset') }}
                    </a-button>
                    <a-button type="primary" :loading="isSaving" @click="handleSave(section)">
                      {{ $t('global_save') }}
                    </a-button>
                  </div>
                </div>
              </a-collapse>
            </a-spin>
          </div>
        </div>
      </div>

      <div class="elevator-nav">
        <a-anchor :affix="false" :offset-top="64" @click="handleAnchorClick">
          <a-anchor-link
            v-for="section in sections"
            :key="section.section_key"
            :href="`#${section.section_key}`"
            :title="`${section.section} - ${section.section_key}`"
          />
        </a-anchor>
      </div>
    </div>

    <a-modal
      v-model="isVideoModalVisible"
      title="Play Video"
      :footer="null"
      :width="800"
      destroy-on-close
      centered
      @cancel="stopVideo"
    >
      <div v-if="currentVideoUrl" class="video-player-container">
        <video
          ref="videoPlayer"
          :src="currentVideoUrl"
          controls
          autoplay
          width="100%"
          height="450"
          @error="handleVideoError"
        ></video>
      </div>
      <div v-else class="ant-empty ant-empty-normal">
        <div class="ant-empty-image">
          <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(0 1)" fill="none" fillRule="evenodd">
              <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7" />
              <g fillRule="nonzero" stroke="#D9D9D9">
                <path
                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                />
                <path
                  d="M41.613 15.931c0-1.605.994-2.962 2.227-2.962.43 0 .833.121 1.175.316V1.83C43.446.8 42.593 0 41.613 0H22.387c-.98 0-1.833.8-1.833 1.83v11.435c.342-.195.745-.316 1.175-.316 1.233 0 2.227 1.357 2.227 2.962H41.613z"
                  fill="#FFF"
                />
              </g>
            </g>
          </svg>
        </div>
        <p class="ant-empty-description">No video URL provided or video failed to load.</p>
      </div>
    </a-modal>

    <input ref="fileInput" type="file" style="display: none" accept="video/*" @change="onFileSelected" />
  </div>
</template>

<script>
import { merchant_language_codes } from '../../../../../admin-cli.config'

export default {
  name: 'GuidanceVideoManagement',
  filters: {
    truncateUrl(value) {
      if (!value) return ''
      const maxLength = 30 // Adjust as needed
      if (value.length > maxLength) {
        return value.substring(0, maxLength) + '...'
      }
      return value
    }
  },
  data() {
    return {
      sections: [],
      originalSections: [], // For reset functionality
      videoMaps: {},
      activeSectionKey: null,
      isVideoModalVisible: false,
      currentVideoUrl: null,
      isLoading: false,
      isSaving: false,
      isUploading: false,
      uploadingInfo: { sectionKey: null, langCode: null },
      languages: merchant_language_codes
    }
  },
  created() {
    this.loadInitialData()
  },
  methods: {
    validatorVideos(rule, value, callback) {
      if (value.find((item) => item.language === 'en_US')?.video_url) {
        return callback()
      }

      return callback(new Error('English video must be provided.'))
    },
    async loadInitialData() {
      this.isLoading = true
      try {
        const data = await ajax.get(ADMIN_API.aidRevamp.getGuidanceVideoMgnData)

        this.originalSections = JSON.parse(JSON.stringify(data))
        this.sections = data

        // Pre-process video data for efficient lookup
        const videoMaps = {}
        this.sections.forEach((section) => {
          const videoMap = {}
          if (section.videos) {
            section.videos.forEach((video) => {
              if (video.language) {
                videoMap[video.language.toUpperCase()] = video
              }
            })
          }
          videoMaps[section.section_key] = videoMap
        })
        this.videoMaps = videoMaps

        if (this.sections && this.sections.length > 0) {
          this.activeSectionKey = this.sections[0].section_key // Initialize first section as active
        }
      } catch (error) {
        console.error('Failed to load guidance video data:', error)
        this.$message.error('Failed to load data. Please try again.')
      } finally {
        this.isLoading = false
      }
    },
    triggerUpload(sectionKey, langCode) {
      this.uploadingInfo = { sectionKey, langCode }
      this.$refs.fileInput.click()
    },
    async onFileSelected(event) {
      const file = event.target.files[0]
      if (!file) return

      const { sectionKey, langCode } = this.uploadingInfo
      const formData = new FormData()
      formData.append('file', file)

      this.isUploading = true
      try {
        // By passing FormData directly, the browser will automatically set
        // the correct Content-Type header with the required boundary.
        // We must not set the Content-Type header manually.
        const response = await ajax.post(ADMIN_API.aidRevamp.uploadGuidanceVideo, {
          data: formData
        })

        if (response && response.video_url) {
          const section = this.sections.find((s) => s.section_key === sectionKey)
          if (section) {
            let video = this.getLangVideo(sectionKey, langCode)
            if (video) {
              video.video_url = response.video_url
            } else {
              // If video for the language does not exist, create it
              if (!section.videos) {
                section.videos = []
              }
              video = { language: langCode, video_url: response.video_url, title: '', desc: '' }
              section.videos.push(video)
              // Update the map
              this.videoMaps[sectionKey][langCode.toUpperCase()] = video
            }
            this.$message.success(`Video for ${langCode} uploaded successfully.`)
          } else {
            this.$message.error('Could not find the section to update.')
          }
        } else {
          this.$message.error('Upload failed: Invalid response from server.')
        }
      } catch (error) {
        console.error('Upload failed:', error)
        this.$message.error('An error occurred during upload. Please try again.')
      } finally {
        this.isUploading = false
        // Reset file input to allow re-uploading the same file
        this.$refs.fileInput.value = ''
        this.uploadingInfo = { sectionKey: null, langCode: null }
      }
    },
    toggleSection(key) {
      if (this.activeSectionKey === key) {
        this.activeSectionKey = null // Collapse if clicking the active section
      } else {
        this.activeSectionKey = key // Expand new section
      }
    },
    getLangVideo(sectionKey, langCode) {
      const sectionMap = this.videoMaps[sectionKey]
      if (sectionMap && langCode) {
        return sectionMap[langCode.toUpperCase()]
      }
      return null
    },
    handleAnchorClick(e, link) {
      e.preventDefault()
      if (!link || !link.href) return

      const sectionKey = link.href.substring(1)
      this.activeSectionKey = sectionKey

      this.$nextTick(() => {
        const element = document.getElementById(sectionKey)
        if (element) {
          element.scrollIntoView()
        }
      })
    },
    handleSave(section) {
      this.isSaving = true
      console.log('Saving section:', section)
      // Placeholder for API call to save the section data
      // On success, update the originalSections to reflect the new baseline
      const index = this.originalSections.findIndex((s) => s.section_key === section.section_key)
      if (index !== -1) {
        this.$refs.form?.[0].validate(async (valid) => {
          if (!valid) {
            this.isSaving = false
            return
          }

          await ajax.post(ADMIN_API.aidRevamp.saveGuidanceVideo, {
            data: section
          })
          this.originalSections[index] = _.cloneDeep(section)
          this.$message.success(`Section ${section.section_key} saved successfully!`)
          this.isSaving = false
        })
      } else {
        this.isSaving = false
      }
    },
    handleReset(section) {
      const originalSection = this.originalSections.find((s) => s.section_key === section.section_key)
      if (originalSection) {
        const sectionIndex = this.sections.findIndex((s) => s.section_key === section.section_key)
        if (sectionIndex !== -1) {
          // Use splice to maintain reactivity
          this.sections.splice(sectionIndex, 1, _.cloneDeep(originalSection))
          this.$message.info(`Section ${section.section_key} has been reset.`)
        }
      }
    },
    playVideo(videoUrl) {
      if (!videoUrl) {
        this.$message.error('Invalid video URL.')
        return
      }
      this.currentVideoUrl = videoUrl
      this.isVideoModalVisible = true
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.play().catch((error) => {
            console.warn('Video autoplay failed:', error)
            this.$message.warning('Video autoplay was blocked. Please click play manually.')
          })
        }
      })
    },
    stopVideo() {
      if (this.$refs.videoPlayer && typeof this.$refs.videoPlayer.pause === 'function') {
        this.$refs.videoPlayer.pause()
      }
      // currentVideoUrl is cleared by destroyOnClose in a-modal
    },
    handleVideoError(event) {
      console.error('Video playback error:', event)
      this.$message.error(
        'Failed to load or play video. The URL might be invalid or the video format is not supported.'
      )
      // Optionally, you could try to clear currentVideoUrl here or show a placeholder in the modal
    },
    handleUpload(sectionKey, langCode) {
      if (this.isUploading) return
      this.triggerUpload(sectionKey, langCode)
    },
    handleReupload(sectionKey, langCode) {
      if (this.isUploading) return
      this.triggerUpload(sectionKey, langCode)
    },
    removeVideo(sectionKey, langCode) {
      const video = this.getLangVideo(sectionKey, langCode)
      if (video) {
        video.video_url = ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.guidance-video-management {
  padding: 24px;
  background-color: #f0f2f5;
  height: calc(100vh - 50px);
  overflow: auto;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 48px);
}

.main-content {
  display: flex;
  gap: 24px;
  height: 100%;
}

.section-list-container {
  flex: 1;
  min-width: 0;
  height: 100%;
  overflow: auto;
}

.section-list {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.section-item {
  border-bottom: 1px solid #e8e8e8;
  &:last-child {
    border-bottom: none;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 16px;
  font-weight: 500;

  &:hover {
    background-color: #f7f7f7;
  }

  &.active {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

.section-title {
  flex-grow: 1;
}

.section-content {
  padding: 24px;
  border-top: 1px solid #e8e8e8;
  background-color: #fff;
}

.video-info {
  width: 100%;

  .video-url-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    a {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 8px;
    }
  }

  .delete-icon {
    cursor: pointer;
    color: #ff4d4f;
    font-size: 14px;
    transition: color 0.3s;

    &:hover {
      color: #d9363e;
    }
  }
}

.section-form {
  margin-bottom: 24px;
}

.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.language-item {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.language-title {
  padding: 8px 12px;
  background-color: #fafafa;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
}

.language-content {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  margin-top: 24px;
  text-align: right;
}

.elevator-nav {
  width: 300px;
  position: sticky;
  top: 0;
  align-self: flex-start;

  ::v-deep .ant-anchor-wrapper {
    background-color: transparent;
    .ant-anchor-ink {
      display: none;
    }
  }
}

.video-player-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  min-height: 450px;
}

video {
  max-height: 70vh;
  display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  .elevator-nav {
    position: static;
    width: 100%;
    margin-bottom: 24px;
  }
}
</style>
