<template>
  <div ref="container" class="date-range-picker-container">
    <a-range-picker
      ref="picker"
      v-model="currValue"
      :dropdown-class-name="dropdownClassName"
      :allow-clear="false"
      :open="open"
      :disabled-date="disabledDateFn"
      @openChange="openChange"
      @change="onChange"
      @calendarChange="calendarChange"
    >
      <a-icon slot="suffixIcon" type="calendar" />
      <template slot="dateRender" slot-scope="current">
        <div
          class="calendar-cell ant-calendar-date"
          :class="{
            '--is-selected': inRangeDate(current) && !isExcludeDate(current),
            '--is-exclude-date-cell': isExcludeDate(current)
          }"
          @click="handleSelect($event, current)"
        >
          <a-icon v-if="displayDeleteDateCell(current)" class="cell-btn --close" type="close" />
          <span class="--text">{{ current.date() }}</span>
        </div>
      </template>
      <div slot="renderExtraFooter" class="extra-footer-box">
        <div class="description">
          <a-button size="small" @click="onClear">
            {{ $t('title_clear') }}
          </a-button>
        </div>
        <div class="actions">
          <a-button size="small" @click="onCancel">{{ $t('global_cancel') }}</a-button>
          <a-button size="small" type="primary" :disabled="allIsExclude" @click="onOk">
            {{ $t('global_submit') }}
          </a-button>
        </div>
      </div>
    </a-range-picker>
  </div>
</template>

<script>
import moment from 'moment'
import { mapMutations, mapState } from 'vuex'
import { getDateRangeValues } from '@activity/utils/index'

export default {
  name: 'PartialDateRangePicker',
  inject: ['$t'],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    excludeDateList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      open: false,
      currStartDate: false, // boolean | date
      currValue: [],
      currExcludeDateList: [],
      dateRangeValues: []
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    allIsExclude() {
      return this.currExcludeDateList.length === this.dateRangeValues.length
    },
    dropdownClassName() {
      const classNameList = ['date-range-picker-content']

      if (!this.currStartDate && this.currValue.length === 2) {
        classNameList.push('has-selected')
      }

      return classNameList.join(' ')
    }
  },
  watch: {
    clickTarget(clickTarget) {
      this.autoClosePickerDebounce(clickTarget)
    },
    open: {
      immediate: true,
      handler(v) {
        if (v) {
          this.initDateRangeValues()
        } else {
          this.$emit('holdExpansion', false)
        }
      }
    }
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    getRangePickerVm() {
      return this.$refs.picker.$refs?.picker
    },
    getCalendarInstanceVm() {
      return this.$refs.picker?.$refs?.picker?.$refs?.calendarInstance
    },
    initDateRangeValues() {
      this.dateRangeValues = this.$store.state?.bulkManageSchedule?.dateRangeValues || []
    },
    disabledDateFn(current) {
      const startDate = this.currStartDate || this.currValue[0]
      if (startDate) {
        return (
          (current && current < moment(startDate).startOf('day')) ||
          current > moment(startDate).add(365, 'days')
        )
      }

      return current && current < moment().startOf('day')
    },
    autoClosePickerDebounce: _.debounce(function autoClosePickerDebounce(clickTarget) {
      if (
        !(
          this.$refs.container.contains?.(clickTarget) ||
          this.$refs.picker?.contains?.(clickTarget) || // calendar picker input
          document.querySelector('.date-range-picker-content')?.contains?.(clickTarget)
        )
      ) {
        if (!this.currValue.length) {
          this.onClear()
        }
        this.onCancel()
      }
    }, 60),
    handleSelect($event, current) {
      const currDate = current.format('YYYY-MM-DD')
      // Stop the previous date
      if (
        this.disabledDateFn(current) ||
        (this.currStartDate && current < moment(this.currStartDate).startOf('day'))
      ) {
        $event.stopPropagation()
        return
      }

      if (this.dateRangeValues.includes(currDate)) {
        $event.stopPropagation()
        if (this.currExcludeDateList.includes(currDate)) {
          this.currExcludeDateList = this.currExcludeDateList.filter((item) => item !== currDate)
        } else {
          this.currExcludeDateList.push(currDate)
        }
        return
      }

      if (!this.currStartDate) {
        this.currStartDate = current
        this.dateRangeValues = []
        this.currExcludeDateList = []
        // 修复 a-range-picker 默认自动高亮到日历右边最后一天的交互
        setTimeout(() => {
          this.getRangePickerVm()?.setState?.({ sHoverValue: [current, current] })
        })
      }
    },
    inRangeDate(current) {
      return this.dateRangeValues.includes(current.format('YYYY-MM-DD'))
    },
    isExcludeDate(current) {
      return this.currExcludeDateList.includes(current.format('YYYY-MM-DD'))
    },
    displayDeleteDateCell(current) {
      if (!this.currValue.length) {
        return false
      }

      const curr = current.format('YYYY-MM-DD')

      return this.dateRangeValues.includes(curr) && !this.currExcludeDateList.includes(curr)
    },
    calendarChange(data) {
      const [start] = data
      // 点击当前已选区间内的 calendar cell 间隙会触发 ant-design-vue/lib/vc-calendar/src/RangeCalendar.js#fireSelectValueChange
      // 导致重新进入选区间状态, 此时 data = [moment, undefined]
      if (this.currValue.length && this.dateRangeValues.includes(moment(start).format('YYYY-MM-DD'))) {
        setTimeout(() => {
          this.getCalendarInstanceVm()?.setState?.({
            firstSelectedValue: null // 抵消影响
          })
          this.getRangePickerVm()?.setState?.({
            sHoverValue: this.currValue // hover 样式区间不变
          })
        })
      }
    },
    openChange(visible) {
      visible && this.resetOriginalValue()
      this.open = true
      this.currValue.length && this.$emit('holdExpansion', !!this.value.length)
    },
    onChange() {
      this.currStartDate = false
      this.currValue.length && (this.dateRangeValues = getDateRangeValues(this.currValue))
    },
    resetOriginalValue() {
      this.currValue = _.cloneDeep(this.value)
      this.currExcludeDateList = _.cloneDeep(this.excludeDateList)
    },
    onClear() {
      this.currValue = []
      this.currExcludeDateList = []
      this.open = true
      this.currStartDate = false
      this.dateRangeValues = []
      // 清除日历选择态
      this.getCalendarInstanceVm()?.clear?.()
    },
    onCancel() {
      this.open = false
      this.currStartDate = false
      this.dateRangeValues = []
      this.resetOriginalValue()
    },
    onOk() {
      this.$emit('change', this.currValue)
      this.$emit('update:excludeDateList', this.currExcludeDateList)
      this.open = false
    }
  }
}
</script>

<style lang="scss">
.date-range-picker-content {
  .calendar-cell {
    position: relative;
    .cell-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 24px;
      opacity: 0.8;
      color: #ff4d4f;
      display: none;
      z-index: 999;
    }
  }

  .ant-calendar-in-range-cell,
  .ant-calendar-selected-start-date,
  .ant-calendar-selected-end-date {
    &::before {
      background-color: transparent !important;
    }

    .calendar-cell {
      width: 28px;
      height: 28px;
      margin: 2px;
      line-height: 24px;
      border: 2px solid transparent;
      background-color: #437dff !important;
      color: #fff;

      &.--is-exclude-date-cell {
        background-color: #fff !important;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  &.has-selected {
    .ant-calendar-selected-start-date,
    .ant-calendar-selected-end-date {
      font-weight: bold;
    }

    .ant-calendar-in-range-cell,
    .ant-calendar-selected-start-date,
    .ant-calendar-selected-end-date {
      .calendar-cell {
        &:hover {
          border-color: #437dff;

          .cell-btn {
            display: block;
          }
        }
        &.--is-selected:hover {
          border-color: #ff4d4f;
        }
      }
    }
  }

  .extra-footer-box {
    display: flex;
    height: 44px;
    line-height: 44px;
    justify-content: space-between;

    .actions {
      display: inline-flex;
      gap: 8px;
      align-items: center;
    }
  }

  .ant-calendar-footer-extra {
    width: 100%;
  }
}
</style>
