<template>
  <div class="select-multi-pass-skus-container">
    <div v-for="(item, index) of reservationSkuIds" :key="item.uuid" class="pass-sku-item">
      <RemoteCascader
        ref="remoteCascader"
        v-model="reservationSkuIds[index].data"
        class="item-cascader"
        v-bind="getInitAttrs(index, TYPE_CONF_DICT.reservation)"
        popup-class-name="select-multi-pass-popup"
        :overlay-style="{
          maxWidth: '500px'
        }"
        :remote-fetch-func="remoteFetchFunc(TYPE_CONF_DICT.reservation)"
        :fmt-options-func="(options, value) => fmtOptionsFunc(options, value, TYPE_CONF_DICT.reservation)"
        :tooltip-func="tooltipFunc"
        :fmt-display-text-func="fmtDisplayTextFunc"
        :can-modified="canModified"
        @showPassSortChange="showPassSortChange"
        v-on="$listeners"
      ></RemoteCascader>

      <template v-if="reservationSkuIds.length > 1 && canDelete">
        <a-popconfirm
          v-if="cacheInitReservationUUIDList.includes(item.uuid)"
          title="Are you sure delete this?"
          ok-text="Yes"
          cancel-text="No"
          @confirm="handleDel(index)"
        >
          <i class="common-delete-btn">
            <svg-icon icon-name="trash" />
          </i>
        </a-popconfirm>

        <i v-else class="common-delete-btn" @click="handleDel(index)">
          <svg-icon icon-name="trash" />
        </i>
      </template>
    </div>

    <a-button v-if="canAddNew" class="btn-add-new" type="link" @click="handleAddNew">+ Add</a-button>
    <a-alert v-if="getVisiable" :message="$t('98428')" type="warning" show-icon />
  </div>
</template>

<script>
import { getUuid } from '@activity/utils'
import ajax from '../../../../../../api/ajax'
import RemoteCascader from './remoteCascader.vue'

export default {
  name: 'SelectMultiPassSkus',
  components: {
    RemoteCascader
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    reservation_sku_ids: {
      type: Array,
      required: true
    },
    multiPassSkuSetting: {
      type: Object,
      required: true
    },
    canModified: {
      type: Boolean,
      default: true
    },
    canAddNew: {
      type: Boolean,
      default: true
    },
    canDelete: {
      type: Boolean,
      default: true
    }
  },
  data() {
    this.TYPE_CONF_DICT = {
      reservation: {
        key: 'reservation',
        dataKey: 'reservationSkuIds',
        optKey: 'reservation_sku',
        apiKey: 'multi_pass_reservation_activities_skus'
      },
      standard: {
        key: 'standard',
        dataKey: 'standardSkuIds',
        optKey: 'standard_sku',
        apiKey: 'multi_pass_standard_activity_skus'
      }
    }

    return {
      /*
        Data format
        {
           [key: string]: {
            initSearchValue?: string,
            searchValue: string,
            placeholder: string,
            [optKey]: Array<any>,
            initTooltip?: string
          }
        }
       */
      optionsList: [],

      reservationSkuIds: [],
      standardSkuIds: [],
      showPassSort: false,
      cacheInitReservationUUIDList: []
    }
  },
  computed: {
    existingReservationSkuIds() {
      return this.reservationSkuIds.reduce((acc, curr) => {
        if (Array.isArray(curr.data) && curr.data.length) {
          return [
            ...acc,
            curr.data[2] // sku id
          ]
        }

        return acc
      }, [])
    },
    existingStandardSkuIds() {
      return this.standardSkuIds.reduce((acc, curr) => {
        if (Array.isArray(curr.data) && curr.data.length) {
          return [
            ...acc,
            curr.data[2] // sku id
          ]
        }

        return acc
      }, [])
    },
    editLanguage() {
      return this.$route.query.lang
    },
    isPass() {
      const subCategoryId = this.$store.state.categoryInfo?.sub_category_id ?? 0
      const categoryIds = process.env.NODE_ENV === 'production' ? [527] : [446]
      return categoryIds.includes(subCategoryId)
    },
    getVisiable() {
      // 是pass且可以新增
      if (this.isPass && this.canAddNew && this.showPassSort) {
        return true
      }
      return false
    }
  },
  watch: {
    multiPassSkuSetting: {
      deep: true,
      immediate: true,
      handler() {
        this.initOptions()
      }
    },
    reservation_sku_ids: {
      deep: true,
      immediate: true,
      handler(v) {
        if (!v.length) {
          this.handleAddNew()
        }
      }
    },
    reservationSkuIds: {
      deep: true,
      handler(v) {
        this.$emit(
          'update:reservation_sku_ids',
          v.map((item) => item.data)
        )
      }
    }
  },
  methods: {
    getInitAttrs(index, constConf) {
      const { key } = constConf

      return this.optionsList?.[index]?.[key] || {}
    },
    isInvalidData(data) {
      return data.every((act) => {
        return (act.packages || []).every((pkg) => Array.isArray(pkg.skus) && pkg.skus.length === 0)
      })
    },
    remoteFetchFunc(constConf) {
      const { apiKey } = constConf
      const url = ADMIN_API.act[apiKey]
      return async (query) => {
        let res = await ajax.get(url, {
          params: {
            keyword: query,
            language: this.editLanguage,
            package_id: +this.$route.query.package_id
          }
        })

        if (res) {
          let data = res.items || []
          if (this.isInvalidData(data)) {
            return []
          } else {
            return data.reduce((accAct, currAct) => {
              return [
                ...accAct,
                {
                  label: `${currAct.activity_id}-${currAct.activity_title}`,
                  value: currAct.activity_id,
                  title: currAct.activity_title,
                  // package level
                  children: (currAct.packages || []).reduce((accPkg, currPkg) => {
                    return [
                      ...accPkg,
                      {
                        label: `${currPkg.package_id}-${currPkg.package_title}`,
                        value: currPkg.package_id,
                        package_title: currPkg.package_title,
                        // sku level
                        children: currPkg.skus.map((sku) => ({
                          label: `${sku.sku_id}-${sku.unit_name}`,
                          value: sku.sku_id,
                          title: sku.unit_name
                        }))
                      }
                    ]
                  }, [])
                }
              ]
            }, [])
          }
        }
      }
    },
    tooltipFunc({ selectedOptions }) {
      const [act, pkg, sku] = selectedOptions

      return `${act.label}<br />${pkg.label}<br />${sku.label}`
    },
    fmtDisplayTextFunc({ selectedOptions }) {
      const [act, pkg, sku] = selectedOptions

      return `${act.value}-${pkg.value}-${sku.title} (${act.title}-${pkg.package_title})`
    },
    fmtOptionsFunc(options, value, constConf) {
      let existingSkusIds =
        this.TYPE_CONF_DICT.reservation.key === constConf.key
          ? this.existingReservationSkuIds
          : this.existingStandardSkuIds

      existingSkusIds = existingSkusIds.filter((item) => item !== value?.[2])

      // 只有 reservation 了
      let existingPkgIds = this.reservationSkuIds.reduce(
        (acc, curr) => (Array.isArray(curr.data) && curr.data.length ? [...acc, curr.data[1]] : acc),
        []
      )
      return (options || []).map((act) => {
        return {
          ...act,
          disabled: this.disabledOnPath(act, existingSkusIds),
          children: act.children.map((pkg) => {
            return {
              ...pkg,
              disabled: existingPkgIds.includes(pkg.value) || this.disabledOnPath(pkg, existingSkusIds),
              children: pkg.children.map((sku) => {
                return {
                  ...sku,
                  disabled: existingSkusIds.includes(sku.value)
                }
              })
            }
          })
        }
      })
    },
    disabledOnPath(data, existingSkusIds) {
      return data.children.every((child) => {
        if (Array.isArray(child.children)) {
          return this.disabledOnPath(child, existingSkusIds)
        }

        return existingSkusIds.includes(child.value)
      })
    },
    showPassSortChange() {
      this.showPassSort = true
    },
    handleAddNew() {
      const { reservation, standard } = this.TYPE_CONF_DICT
      this.$set(this, 'optionsList', [
        ...this.optionsList,
        {
          [reservation.key]: {
            searchValue: '',
            placeholder: this.$t('77684'),
            [reservation.optKey]: []
          },

          [standard.key]: {
            searchValue: '',
            placeholder: this.$t('77685'),
            [standard.optKey]: []
          }
        }
      ])

      this.reservationSkuIds.push({
        data: [],
        uuid: getUuid()
      })
      this.standardSkuIds.push({
        data: [],
        uuid: getUuid()
      })
    },
    handleDel(index) {
      this.optionsList.splice(index, 1)
      this.standardSkuIds.splice(index, 1)
      this.showPassSort = false
      const del = this.reservationSkuIds.splice(index, 1)
      this.$emit('delete', del?.[0]?.data ?? [])
    },
    getTooltip(data) {
      const { activity_id, package_id, sku_id, activity_name, package_name, unit_name } = data || {}

      return data
        ? `${activity_id}-${activity_name}<br />${package_id}-${package_name}<br />${sku_id}-${unit_name}`
        : ''
    },
    getValue(data) {
      const { activity_id, package_id, sku_id } = data || {}

      return data ? [activity_id, package_id, sku_id] : []
    },
    getSearchValue(data) {
      const { activity_id, package_id, activity_name, package_name, unit_name } = data || {}

      return data ? `${activity_id}-${package_id}-${unit_name} (${activity_name} - ${package_name})` : ''
    },
    async initOptions() {
      const sku_id = this.data?.sku_id ?? null

      if (sku_id && this.multiPassSkuSetting?.skus) {
        const { reservation, standard } = this.TYPE_CONF_DICT

        this.$set(
          this,
          'optionsList',
          this.multiPassSkuSetting.skus.map((item) => {
            const { reservation_sku, standard_sku } = item

            return {
              [reservation.key]: {
                searchValue: '',
                initSearchValue: this.getSearchValue(reservation_sku),
                placeholder: this.$t('77684'),
                [reservation.optKey]: [],
                initTooltip: this.getTooltip(reservation_sku)
              },
              [standard.key]: {
                searchValue: '',
                initSearchValue: this.getSearchValue(standard_sku),
                placeholder: this.$t('77685'),
                [standard.optKey]: [],
                initTooltip: this.getTooltip(standard_sku)
              }
            }
          })
        )

        const { reservationSkuIds, standardSkuIds } = this.multiPassSkuSetting.skus.reduce(
          (acc, curr) => {
            const { reservation_sku, standard_sku } = curr

            return {
              reservationSkuIds: [
                ...acc.reservationSkuIds,
                {
                  uuid: getUuid(),
                  data: this.getValue(reservation_sku)
                }
              ],
              standardSkuIds: [
                ...acc.standardSkuIds,
                {
                  uuid: getUuid(),
                  data: this.getValue(standard_sku)
                }
              ]
            }
          },
          {
            reservationSkuIds: [],
            standardSkuIds: []
          }
        )

        this.$set(this, 'reservationSkuIds', reservationSkuIds)
        this.cacheInitReservationUUIDList = reservationSkuIds.map((item) => item.uuid)
        this.$set(this, 'standardSkuIds', standardSkuIds)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.select-multi-pass-skus-container {
  .pass-sku-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .item-cascader {
      width: calc((100% - 32px) / 2);
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .btn-add-new {
    padding-left: 0;
  }
}
</style>

<style lang="scss">
.select-multi-pass-popup .ant-cascader-menu {
  max-width: 30vw;

  .ant-cascader-menu-item {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
