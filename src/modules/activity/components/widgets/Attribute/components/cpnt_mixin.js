export default {
  props: {
    ref_data: Object,
    disabled: <PERSON><PERSON>an, // alais with `chosen`
    chosen: <PERSON><PERSON>an,
    row: Object,
    var_map: Object,
    type: String,
    config: { type: String, default: '{}' },
    data_var_map: { type: Object, default: () => {} },
    form: [Number, String, Array, Date],
    display_html: { type: String, default: () => '' }
  },
  computed: {
    formItemLabel() {
      return this.row.key.replace(/(^{{)|(}}$)/g, '')
    }
  }
}
