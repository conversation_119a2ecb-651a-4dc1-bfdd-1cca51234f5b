<template>
  <div class="trigger-name-to-view" :class="{ '--pointer': list.length }" @click="handleTrigger">
    <slot name="title"></slot>
  </div>
</template>

<script>
export default {
  name: 'TriggerNameToView',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  computed: {
    sortedData() {
      return _.clone(this.list).sort((a, b) => a - b)
    }
  },
  watch: {
    list: {
      immediate: true,
      deep: true,
      handler() {
        this.refreshIndex()
      }
    }
  },
  methods: {
    refreshIndex() {
      this.currentIndex = 0
    },
    handleTrigger() {
      if (!this.list.length) return

      this.$emit('scrollToViewIndex', {
        index: this.currentIndex,
        value: this.sortedData[this.currentIndex]
      })

      this.currentIndex++
      if (this.currentIndex >= this.list.length) {
        this.refreshIndex()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.trigger-name-to-view {
  &.--pointer {
    user-select: none;
    cursor: pointer;
  }
}
</style>
