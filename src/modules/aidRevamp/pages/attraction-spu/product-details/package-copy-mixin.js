import { mapState, mapActions } from 'vuex'

export default {
  inject: ['refreshPkgs', 'refreshPage', 'rootChangeLoading'],
  data() {
    return {
      copyDetailVisible: false,
      allGroups: []
    }
  },
  props: {
    activity_id: {
      type: Number,
      required: true
    },
    package_id: {
      type: Number,
      required: true
    }
  },
  computed: {
    ...mapState('section', {
      groupsSummary: (state) => state.selectionData?.basic_group?.groups_summary || []
    }),
    groupList() {
      return this.groupsSummary.map((group) => ({
        name: group.name,
        id: group.id
      }))
    },
    ...mapState({
      pkgInfos: 'pkgInfos'
    }),
    packageList() {
      const packages = (this.pkgInfos && this.pkgInfos.packages) || []
      return packages
        .filter((item) => item.package_id !== +this.package_id)
        .map((item) => ({
          value: item.package_id,
          label: this.getName(item)
        }))
    }
  },
  async created() {
    await this.initPkgId(this.package_id)

    this.$nextTick(() => {
      this.initLoadFinish = true
    })
  },
  methods: {
    ...mapActions(['getPkgInfo2actions']),
    getName(data) {
      let current = _.find(data.package_local || [], {
        language_type: lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
      })
      return (current && current.name) || _.get(data, 'package_local[0].name', '')
    },
    async getPackagesBasicInfo(params = {}, isEditData = false) {
      return await this.fetchData?.(params, isEditData)
    },
    async initPkgId(package_id) {
      let reqData = await this.getPkgInfo2actions({
        activity_id: this.activity_id,
        refresh: true
      })
      klook.bus.$emit('updatePkgInfos2bus', reqData)
      this.reqData = reqData

      let params = {
        package_id,
        language: klook.getEditLang(),
        refer_language: this.refer_language,
        page_from: klook.getPlatformRoleKey()
      }

      this.defaultPackageInfo = reqData.default_package_info
      let result = await this.getPackagesBasicInfo(params, true)

      this.get_packages_basic_info = {
        package_id: this.package_id,
        ...(result || {})
      }
    },
    handleShowCopyDetailModal() {
      this.copyDetailVisible = true
    },
    async refreshWhenFrom() {
      await this.refreshPkgs()
      await this.refreshPage()
    }
  }
}
