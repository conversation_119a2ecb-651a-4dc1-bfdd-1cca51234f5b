<!-- eslint-disable vue/valid-v-bind-sync -->
<template>
  <div class="photo-group">
    <!-- 点击区域块会冒泡到按钮并触发点击，若 @click.prevent.stop CheckBox 会点击失效，待确认原因 -->
    <a-button v-show="false"></a-button>
    <template v-if="uploadView">
      <UploadView
        v-bind="uploadView"
        :image-url-prefix="imageUrlPrefix"
        :images.sync="current_list"
        :upload-text="calcUploadText"
        :hide-upload="!!(uploadView.fieldKey === 'activity_banner' && current_list.length)"
        :focus-list="currentFocusList"
        @upload="uploadHandler"
        @delete="deleteHandler"
      ></UploadView>
      <div
        v-if="uploadView.fieldKey === 'activity_picture' && current_list && current_list.length"
        class="act-picture-box js-dataset-field"
        data-field="picture_captions"
      >
        <div class="act-picture-box__title">
          {{ $t('83955') }}
        </div>
        <a-input-group v-for="(obj, i) in current_list" :key="i" compact>
          <a-input
            v-model="obj.desc"
            :addon-before="$t('83959', { num: i + 1 })"
            :placeholder="$t('global_please_input')"
            @focus="currentFocusList = [i]"
            @blur="currentFocusList = []"
          />
        </a-input-group>
      </div>
    </template>
    <template v-else>
      <div class="photo-group__upload">
        <!-- banner 时, 已有图片 => 文案 => 重新上传,  没有图片 => 文案 => 添加  -->
        <a-button
          v-if="show_replace_button"
          type="primary"
          class="pic-upload-btn"
          :disabled="disabled"
          :loading="uploading"
          @click="replaceOne"
        >
          {{ current_list.length ? $t('global_reupload') : $t('global_button_add') }}
        </a-button>
        <a-button
          v-if="show_add_button"
          type="primary"
          class="pic-upload-btn"
          :disabled="disabled"
          :loading="uploading"
          @click.self="addNew2"
        >
          {{ $t('global_button_add') }}
        </a-button>
        <!-- 从已有的图片复制 -->
        <a-button v-show="!hideCopy && calcAllLangImgList.length" :disabled="disabled" @click="showDialog">
          {{ $t('create_photo_copy') }}
        </a-button>
      </div>

      <div class="no-photo-tip" v-if="current_list.length === 0">
        {{ $t('21856') }}
      </div>

      <!-- 创建当前语言的图片 -->
      <div class="photo-group__langonly" v-if="showCurLangOnly">
        <a-checkbox v-model="cur_lang_only_model" :disabled="disabled || disabledCurLangOnly">
          <span :data-spm-module="getSpm" data-spm-virtual-item="__virtual">{{ curLangOnlyLabel }}</span>
        </a-checkbox>
      </div>

      <!-- 每个尺寸的图片 -->
      <div class="photo-group__imagelist">
        <!-- <draggable v-model="current_list">
        <transition-group>-->
        <DraggableList
          v-model="current_list"
          handle
          item-key="name"
          :disabled="!canDrag"
          @end="handleDragEnd"
        >
          <template #default="{ item: photo, index }">
            <div class="__option-content">
              <Option
                :ref="'option' + index"
                :key="photo.name"
                :current_list="current_list"
                :need-set-size="needSetSize"
                :need-set-no-crop-banner="needSetNoCropBanner"
                :need-display-as-banner="needDisplayAsBanner"
                :need-custom-size="needCustomSize"
                :size-conf="sizeConf"
                :need-desc="needDesc"
                :need-alt="needAlt"
                :disabled-alt="disabledAlt"
                :disabled-desc="disabledDesc"
                :need-order="needOrder"
                :activity-id="activityId"
                :total="totalLength"
                :config.sync="photo"
                :ref-config="getRefConfig(photo)"
                :allow-remove="allowRemove"
                :is-single="isSingle"
                :allow-download-image="allowDownloadImage"
                :desktop-ratio="desktopRatio"
                :disabled="disabled"
                :need-display-is-card="needDisplayIsCard"
                :banner-must-be-clear="bannerMustBeClear"
                v-bind="$attrs"
                :cur-lang="curLang"
                :word-count-limit="wordCountLimit"
                @remove="handleRemove(photo, index)"
                @banner-count-change="$emit('banner-count-change')"
                @changeOrder="$emit('validate-form')"
                @changeIsCard="(event) => changeIsCard(event, index)"
              />
              <svg-icon
                v-if="canDrag"
                class="handle"
                icon-name="menu"
                style="margin-left: 12px; font-size: 16px"
                @click.stop.prevent
              />
            </div>
          </template>
        </DraggableList>
        <!-- </transition-group>
        </draggable>-->
      </div>
    </template>
    <a-modal v-model="dialog_visible" :width="870" class="submit-confirm-dialog-2" title="Select">
      <div>
        <a-tabs type="card" v-model="current_tab">
          <a-tab-pane
            v-for="lang in dialog_shown_list"
            :key="lang.language_type"
            :tab="getLangText(lang.language_type)"
          >
            <!-- contnent here -->
            <CommonCheckBox v-model="selected[lang.language_type]" :items="lang.list" :show-all="false" align>
              <template slot-scope="data" v-for="(image, index) in lang.list" :slot="index">
                <div :key="image.name">
                  <img style="width: 200px; height: 100px" :src="imageUrlPrefix + data.data.value" alt />
                </div>
              </template>
            </CommonCheckBox>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div slot="footer">
        <a-button @click="hideDialog"> Close </a-button>
        <a-button type="primary" @click="confirmCopy"> Confirm </a-button>
      </div>
    </a-modal>
  </div>
</template>
<script>
import Option from './option'
import UploadView from '@activity/pages/activityManagement/act-create/main/component/upload-view.vue'
import {
  cloudinaryOptions,
  imageUrlPrefix,
  SUPPORT_LARGER_PIC_CONF,
  HIGH_QUALITY_PIC_CONF,
  HIGH_QUALITY_PIC_SIZE,
  showCloudinaryUploadTips
} from '../const'
import { formatImageSize, formatTextId } from '../utils'
const lang_conf = require('lang_conf')
import CommonCheckBox from '@activity/components/common-checkbox'
import { desktopRatio } from '../const'
import DraggableList from '../../DraggableList'
import { getWordCountLimit } from '@activity/utils'

export default {
  name: 'PhotoGroup',
  provide: {
    isInBannerBlock() {
      return this.$attrs?.attr_id === 'activity_banner'
    }
  },
  inject: ['updateData'],
  components: { DraggableList, Option, CommonCheckBox, UploadView },
  props: {
    uploadView: {
      type: Object,
      default: null
    },
    minBannerCount: {
      default: 0
    },
    //! activity_id 上传图片需要 activity
    activityId: {
      type: [Number, String],
      default: '',
      required: true
    },
    //! 所有语言的所有图片, 用于弹窗复制
    listAll: {
      type: Array,
      default: () => [],
      required: true
    },
    //! 当前编辑的图片列表
    list: {
      type: Array,
      default: () => [],
      required: true
    },
    //! 限制最大数量
    limit: {
      type: Number,
      default: 20 // 最大 20
    },
    //! 展示新增按钮
    allowAdd: {
      type: Boolean,
      default: true
    },
    //! 允许替换
    allowReplace: {
      type: Boolean,
      default: false
    },
    //! 展示删除按钮
    allowRemove: {
      type: Boolean,
      default: true
    },
    //! 是否只作用于当前语言
    curLangOnly: {
      type: Boolean,
      default: false
    },
    //! 是否只作用于当前语言 文案
    curLangOnlyLabel: {
      type: String,
      default() {
        return this.$t('create_act_photo_for_lang')
      }
    },
    //! needCustomSize
    needCustomSize: {
      type: Boolean,
      default: false
    },
    //! 不裁剪原图设置banner勾选框展示与否
    needSetNoCropBanner: {
      type: Boolean,
      default: true
    },
    //! 控制展示为 banner checkox 展示与否
    needDisplayAsBanner: {
      type: Boolean,
      default: true
    },
    //! 需要图片alt
    needAlt: {
      type: Boolean,
      default: true
    },
    disabledAlt: {
      type: Boolean,
      default: false
    },
    needValidAlt: {
      type: Boolean,
      default: true
    },
    //! 需要图片描述
    needDesc: {
      type: Boolean,
      default: true
    },
    disabledDesc: {
      type: Boolean,
      default: false
    },
    //! 需要图片排序
    needOrder: {
      type: Boolean,
      default: true
    },
    //! 参考语种, 不传即不需要参考语言
    refLang: {
      type: String,
      default: ''
    },
    //! 当前语言
    curLang: {
      type: String,
      default: ''
    },
    //! 展示 => 仅创建此语言的图片
    showCurLangOnly: {
      type: Boolean,
      default: true
    },
    disabledCurLangOnly: {
      type: Boolean,
      default: false
    },
    //! 尺寸配置
    sizeConf: {
      type: Object,
      default: () => {}
    },
    // 只展示单张 , 不展示 desktop mobile - 活动指引图片
    isSingle: {
      type: Boolean,
      default: false
    },
    // 允许下载原图
    allowDownloadImage: {
      type: Boolean,
      default: true
    },
    // 需不需要校验 desc
    needValidDesc: {
      type: Boolean,
      default: true
    },
    // banner数量
    bannerCount: {
      type: Number,
      default: 0
    },
    // banner数量
    triggerBannerCheck: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    needDisplayIsCard: {
      type: Boolean,
      default: false
    },
    needSetSize: {
      type: Boolean,
      default: true
    },
    hideCopy: {
      type: Boolean,
      default: false
    },
    supportUploadLongImage: {
      type: Boolean,
      default: false
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentFocusList: [],
      //! 弹窗
      dialog_visible: false,
      //! 当前语言下的图片列表
      current_list: [],
      //! 复制弹窗中的当前 tab id
      current_tab: 0,
      //! 复制弹窗中 => 每个语言tab下的选中值
      selected: {},
      //! 预览 url
      imageUrlPrefix,
      // desktop 比例
      desktopRatio,
      uploading: false
    }
  },
  computed: {
    wordCountLimit() {
      return getWordCountLimit(this.$attrs?.word_count_limit ?? [], this.curLang)
    },
    canDrag() {
      return this.needOrder && this.current_list.length > 1
    },
    //! 和 add button 不同, 这个按钮用于 banner, banner 只有一张图图片,  这里会替换第一张图
    show_replace_button() {
      return this.allowReplace
    },
    //! 展示上传按钮
    show_add_button() {
      //! 配置为允许 && 数组未超过限制
      return this.allowAdd && this.limit > this.current_list.length
    },
    //! 是否仅作用于当前语言
    cur_lang_only_model: {
      get() {
        return this.curLangOnly
      },
      set: async function (val) {
        if (val) {
          this.$confirm({
            title: this.$t('image_change_custom_hint'),
            onOk: () => {
              this.$emit('update:cur-lang-only', val)
            }
          })
        } else {
          this.$confirm({
            title: this.$t('75065'),
            onOk: () => {
              this.$emit('update:cur-lang-only', val)
            }
          })
        }
      }
    },
    calcAllLangImgList() {
      const list = (this.dialog_shown_list || []).reduce((arr, obj) => {
        arr.push(...(obj.list || []))
        return arr
      }, [])
      return list
    },
    //! copy 弹窗展示的语言列表
    dialog_shown_list() {
      //! 展示 非 All
      let list = (this.listAll.filter((v) => v.list && v.list.length && v.language_type !== 'ALL') || []).map(
        (langObj) => {
          return {
            language_type: langObj.language_type,
            // list: (langObj.list.filter((v) => v.image.length) || []).map((v) => {
            list: (langObj.list || []).map((v) => {
              return {
                ...v,
                key: _.get(v, 'image[0].name', v.name),
                value: _.get(v, 'image[0].name', v.name)
              }
            })
          }
        }
      )

      return list
    },
    totalLength() {
      return this.current_list.length
    },
    getSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `LocalOnlyPhoto?oid=${oid}&trg=manual`
    },
    calcUploadText() {
      const { uploadView, current_list } = this
      if (this.show_replace_button) {
        return current_list?.length ? this.$t('83893') : this.$t('83891')
      } else if (this.show_add_button) {
        return this.$t('83891')
      }
      const map = {
        activity_banner: this.$t('83893'),
        activity_picture: this.$t('83891')
      }
      return map[uploadView?.fieldKey]
    }
  },
  watch: {
    current_list: {
      deep: true,
      handler(v) {
        this.$emit('current-list-change', v || [])
      }
    },
    //! 格式转换
    list: {
      immediate: true,
      handler(next) {
        this.current_list = next.map(this.format)
      }
    },
    // banner 数量变动时, 更改 desktop 图片比例
    bannerCount: {
      immediate: true,
      handler(next, old) {
        if (next === 0) return
        // banner 数量大于两张时, 图片的比例变为 960:460
        if (next > 1) {
          this.desktopRatio = '960:460'
          this.current_list = this.current_list.map((v) => {
            if (v.DESKTOP.pre_process === '' || v.DESKTOP.pre_process.includes('1160:460')) {
              v.DESKTOP.pre_process = v.DESKTOP.pre_process.replace('960:460', '1160:460')
              v.DESKTOP.url = v.DESKTOP.url?.replace('1160:460', '960:460') || ''
            }
            return v
          })
        } else {
          this.desktopRatio = '1160:460'
          this.current_list = this.current_list.map((v) => {
            if (v.DESKTOP.pre_process === '' || v.DESKTOP.pre_process.includes('1160:460')) {
              v.DESKTOP.pre_process = v.DESKTOP.pre_process.replace('960:460', '1160:460')
              v.DESKTOP.url = v.DESKTOP.url?.replace('960:460', '1160:460') || ''
            }
            return v
          })
        }
      }
    },
    triggerBannerCheck() {
      if (this.bannerCount > 1) {
        this.current_list = this.current_list.map((v) => {
          v.DESKTOP.pre_process = v.DESKTOP.pre_process
            .replace(/c_crop[^/]*\//g, '')
            .replace('1160:460', '960:460')
          v.DESKTOP.url = v.DESKTOP.url?.replace(/c_crop[^/]*\//g, '').replace('1160:460', '960:460')
          return v
        })
      } else {
        this.current_list = this.current_list.map((v) => {
          v.DESKTOP.pre_process = v.DESKTOP.pre_process
            .replace(/c_crop[^/]*\//g, '')
            .replace('960:460', '1160:460')
          v.DESKTOP.url = v.DESKTOP.url?.replace(/c_crop[^/]*\//g, '').replace('960:460', '1160:460')
          return v
        })
      }
    },
    //! 复制弹窗中展示的图片
    dialog_shown_list(next) {
      if (next.length) {
        this.current_tab = next[0].language_type
      }
      ;(next || []).forEach((v) => {
        this.$set(this.selected, v.language_type, [])
      })
    }
  },
  mounted() {
    const that = this
    klook.bus.$on('clearCheckIsCard', function () {
      that.$set(
        that,
        'current_list',
        that.current_list.map((item) => ({
          ...item,
          is_card: false
        }))
      )
    })
  },
  beforeDestroy() {
    klook.bus.$off('clearCheckIsCard')
    this.clearCloudinaryWaiter()
  },
  methods: {
    deleteHandler(item, i) {
      this.handleRemove(item, i)
    },
    uploadHandler() {
      const { uploadView } = this
      if (this.show_replace_button) {
        this.replaceOne()
      } else if (this.show_add_button) {
        this.addNew2(uploadView.fieldKey)
      } else {
        const map = {
          activity_banner: this.replaceOne,
          activity_picture: this.addNew2
        }
        map[uploadView?.fieldKey] && map[uploadView.fieldKey](uploadView.fieldKey)
      }
    },
    changeIsCard(checked, index) {
      klook.bus.$emit('clearCheckIsCard')

      this.$nextTick(() => {
        Object.assign(this.current_list[index], {
          is_card: checked
        })
      })
    },
    handleDragEnd() {
      this.$set(
        this,
        'current_list',
        this.current_list.map((item, index) => ({
          ...item,
          display_order: index + 1
        }))
      )
    },
    async addNew2(type) {
      if (this.wid) {
        this.wid?.destroy?.()
      }
      this.$message.info({
        content: klook.parseStr1(this.$t('28992'), { 20: this.limit }),
        duration: 6
      })

      this.uploading = true
      const obj = await this.uploadPhoto(this.limit)
      this.uploading = false
      if (!obj) {
        return
      }

      let arr = obj.files.filter((o) => o.uploadInfo).map((o) => o.uploadInfo)
      arr.forEach((info, i) => {
        this.addNew(info)
        if (type === 'activity_banner') {
          this.changeIsCard(true, i)
          return
        }
      })
      return arr
    },
    //! 新增
    async addNew(info) {
      // 从第二张图片开始默认勾上 "展示为banner"
      const { bannerMustBeClear } = this
      const banner_display = !bannerMustBeClear && this.current_list.length >= 1

      this.current_list.forEach((item, index) => {
        Object.assign(item, {
          display_order: index + 1
        })
      })

      this.current_list.push({
        is_default: false,
        banner_display, // 从第二张图片开始默认勾上 "展示为banner"
        create: true, // 是否为新建,
        alt: '',
        desc: '',
        display_order: this.current_list.length + 1,
        name: info.url.split('/').pop(),
        customSize: 'origin-size',
        height: info.height,
        width: info.width,
        ...(this.needDisplayIsCard ? { is_card: false } : {}),
        ORIGIN: {
          height: info.height,
          url: info.url,
          name: info.url.split('/').pop(),
          pre_process: '',
          size_type: 'ORIGIN',
          width: info.width,
          is_no_crop_banner: false,
          size: info.bytes
        },
        DESKTOP: {
          height: info.height,
          url: info.url,
          name: info.url.split('/').pop(),
          pre_process: '',
          size_type: 'DESKTOP',
          width: info.width,
          is_no_crop_banner: false,
          size: info.bytes
        },
        MOBILE: {
          height: info.height,
          url: info.url,
          name: info.url.split('/').pop(),
          pre_process: '',
          size_type: 'MOBILE',
          width: info.width,
          is_no_crop_banner: false,
          size: info.bytes
        }
      })
      this.$emit('banner-count-change')
      this.$emit('validate-form')
    },
    //! 上传, 替换第一张 --- 用于banner, 顺序置为 1
    async replaceOne(type) {
      this.uploading = true
      let obj = await this.uploadPhoto(1)
      this.uploading = false

      if (!obj) {
        return
      }

      let info = obj.files[0].uploadInfo
      if (type === 'activity_banner') {
        this.current_list = []
        this.addNew(info)
        this.changeIsCard(true, 0)
        return
      }
      let photo = {
        is_default: false,
        banner_display: false,
        alt: '',
        desc: '',
        display_order: 1,
        height: info.height,
        width: info.width,
        name: info.url.split('/').pop(),
        ORIGIN: {
          height: info.height,
          url: info.url,
          name: info.url.split('/').pop(),
          pre_process: '',
          size_type: 'ORIGIN',
          width: info.width,
          is_no_crop_banner: false,
          size: info.bytes
        },
        DESKTOP: {
          height: info.height,
          url: info.url,
          name: info.url.split('/').pop(),
          pre_process: '',
          size_type: 'DESKTOP',
          width: info.width,
          is_no_crop_banner: false,
          size: info.bytes
        },
        MOBILE: {
          height: info.height,
          url: info.url,
          name: info.url.split('/').pop(),
          pre_process: '',
          size_type: 'MOBILE',
          width: info.width,
          is_no_crop_banner: false,
          size: info.bytes
        }
      }
      // 已有? 替换
      if (this.current_list.length) {
        // 保留旧信息
        photo.alt = this.current_list[0].alt
        photo.desc = this.current_list[0].desc
        // 替换第一张图
        this.current_list.splice(0, 1, photo)
        // 否则新增
      } else {
        this.current_list = [photo]
        this.$emit('banner-count-change')
        this.$emit('validate-form')
      }
    },
    clearCloudinaryWaiter() {
      this.cloudinaryTimer && clearInterval(this.cloudinaryTimer)
      this.waitCloudinaryLoadedResolve?.(false)
    },
    waitCloudinaryLoaded() {
      this.clearCloudinaryWaiter()

      const max = 30
      let count = 0
      return new Promise((resolve) => {
        this.waitCloudinaryLoadedResolve = resolve
        this.cloudinaryTimer = setInterval(() => {
          count += 1

          if (window.cloudinary || max < count) {
            clearInterval(this.cloudinaryTimer)
            this.waitCloudinaryLoadedResolve = this.cloudinaryTimer = null
            resolve(window.cloudinary)
          }
        }, 200)
      })
    },
    //! 上传
    async uploadPhoto(number = 1) {
      // 如果要求高质量，就加入限制
      const { bannerMustBeClear, isInBannerBlock } = this
      const currPhotoMustBeClear = bannerMustBeClear && isInBannerBlock
      const cloudinaryOpt = cloudinaryOptions(this.activityId, {
        ...(this.supportUploadLongImage ? SUPPORT_LARGER_PIC_CONF : {}),
        ...(currPhotoMustBeClear ? HIGH_QUALITY_PIC_CONF : {})
      })
      let opt = _.merge(cloudinaryOpt, {
        multiple: number > 1,
        maxFiles: number
      })
      if (!window.cloudinary && !(await this.waitCloudinaryLoaded())) {
        return
      }

      const maxImageWidth = opt.maxImageWidth || 3000
      const maxImageHeight = opt.maxImageHeight || Math.pow(10, 5)
      const { minImageHeight, minImageWidth } = HIGH_QUALITY_PIC_CONF
      const minFileSize = HIGH_QUALITY_PIC_SIZE
      return new Promise((resolve) => {
        let div
        let wid = window.cloudinary.createUploadWidget(opt, (error, result) => {
          // error
          if (error) {
            this.$emit('error', error)
          } else if (result.event === 'source-changed') {
            div = showCloudinaryUploadTips()
          } else if (result.event === 'display-changed' && div) {
            div.style.display = result.info === 'minimized' ? 'none' : 'inherit'
          } else if (result.event === 'queues-end') {
            if (!result.info) {
              result.info = {}
            }

            let invalidImages = []
            let lowQualityImages = []
            result.info.files = (result.info.files || [])
              .filter((file) => file.uploadInfo?.url)
              .filter((file) => {
                // 上传图片大于40M时，maxImageWidth、maxImageHeight和 validateMaxWidthHeight 会失效
                const { url, width, height, bytes } = file.uploadInfo
                const invalid = width > maxImageWidth || height > maxImageHeight
                const isLowQuality = width < minImageWidth || height < minImageHeight || bytes < minFileSize

                const temp = url.split('/')
                const fileName = temp[temp.length - 1]

                if (invalid) {
                  invalidImages.push(`${fileName}: ${width}X${height}`)
                }

                if (currPhotoMustBeClear && isLowQuality) {
                  lowQualityImages.push(`${fileName}: ${width}X${height}, size: ${formatImageSize(bytes)}`)
                }

                const qualityValid = !currPhotoMustBeClear || (currPhotoMustBeClear && !isLowQuality)

                return !invalid && qualityValid
              })
              .map((file) => {
                file.uploadInfo.url = file.uploadInfo.url.replace('http', 'https')
                return file
              })

            if (invalidImages.length) {
              this.$message.warn({
                content(h) {
                  return h('label', [
                    'Image dimensions (',
                    h('b', { style: { color: 'red' } }, `${invalidImages.join(',')}`),
                    `) are bigger than the maximum allowed: (${maxImageWidth}X${maxImageHeight})`
                  ])
                },
                duration: 10
              })

              if (!result.info.files.length) {
                wid.close()
              }
            }

            if (lowQualityImages.length) {
              this.$message.warn(
                formatTextId(this.$t('193820'), {
                  minfilesize: formatImageSize(minFileSize),
                  minwidth: minImageWidth,
                  minheight: minImageHeight
                })
              )

              if (!result.info.files.length) {
                wid.close()
              }
            }

            resolve(result.info)
          } else if (['close', 'batch-canceled'].includes(result.event)) {
            result.event === 'batch-canceled' &&
              this.$message.error(klook.parseStr1(this.$t('28992'), { 20: this.limit }))
            wid.destroy({ removeThumbnails: true }).then(() => {
              console.log('Widget was destroyed')
            })

            if (result.event === 'close') {
              div?.remove?.()
              this.wid = null
            }
          }
        })
        this.wid = wid
        wid.open()
      })
    },
    //! 格式转换
    format(photo) {
      //! 转换为对象格式
      // ORIGIN: {}
      // DESKTOP: {}
      // MOBILE: {}
      let ORIGIN = photo.image.find((v) => {
        return v.size_type === 'ORIGIN'
      })
      let DESKTOP = photo.image.find((v) => {
        return v.size_type === 'DESKTOP'
      })
      let MOBILE = photo.image.find((v) => {
        return v.size_type === 'MOBILE'
      })
      //! 判断是否自定义尺寸
      if (this.needCustomSize) {
        if (`${photo.width}_${photo.height}` === `${this.sizeConf.width}_${this.sizeConf.height}`) {
          photo.customSize = 'custom-size'
        } else {
          photo.customSize = 'origin-size'
        }
      }
      photo.name = ORIGIN.name
      //! 转换为 boolean
      photo.is_default = !!photo.is_default
      photo.banner_display = !!photo.banner_display
      //! 是否模糊处理
      photo.is_no_crop_banner =
        DESKTOP.pre_process.includes('u_activities') || MOBILE.pre_process.includes('u_activities')

      if (this.needDisplayIsCard) {
        photo.is_card = !!DESKTOP.is_card
      }

      photo.size = ORIGIN.size
      let config = {
        ORIGIN,
        DESKTOP,
        MOBILE,
        ...photo
      }
      return config
    },
    //! 展示复制弹窗
    showDialog() {
      this.dialog_visible = true
    },
    //! 确认复制
    confirmCopy() {
      // TODO
      let addItems = []
      // add
      Object.entries(this.selected).forEach(([key, value]) => {
        let langObj =
          this.dialog_shown_list.find((obj) => {
            return obj.language_type === key
          }) || {}
        // console.log(langObj);
        let item = langObj.list.filter((v) => {
          return value.includes(v.image[0].name)
        })
        item && addItems.push(...item)
      })
      // console.log("addItems", addItems);
      this.$emit('copy-photo', addItems, this.type)
      this.$emit('banner-count-change')
      this.$emit('validate-form')
      this.hideDialog()
    },
    //! 关闭弹窗
    hideDialog() {
      this.dialog_visible = false
    },
    //! 删除
    handleRemove(photo, index) {
      this.current_list.splice(index, 1)
      this.$emit('banner-count-change')
      this.updateData && this.updateData()
    },
    //! 获取语言对应的text
    getLangText(lang) {
      return lang === 'ALL' ? 'Other Languages' : lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[lang]
    },
    //! 获取数据
    getList() {
      if (this.needCustomSize) {
        return _.cloneDeep(this.current_list).map((v) => {
          // 传原始尺寸 or 裁剪尺寸
          if (v.customSize === 'custom-size') {
            return {
              ...v,
              ...this.sizeConf
            }
          } else {
            return {
              ...v,
              ...v.origin_size
            }
          }
        })
      }
      return this.current_list
    },
    // 校验图片
    validateName() {
      // 是否有空图片
      return this.current_list.every((photo) => photo.name)
    },
    // 判断图片最大限制
    validateLimit() {
      let leng = this.getList().length
      if (leng > this.limit) {
        return {
          valid: false,
          field: '',
          message: klook.parseStr1(this.$t('28992'), { 20: this.limit })
        }
      }
      // 成功
      return {
        valid: true,
        field: '',
        message: this.$t('global_images') + this.$t('global_show_msg_save_success')
      }
    },
    // 判断是否有图片
    validateLength() {
      let leng = this.getList().length
      if (!leng || leng < this.minBannerCount) {
        return {
          valid: false,
          field: '',
          message: 'Please upload photo'
        }
      }
      // 成功
      return {
        valid: true,
        field: '',
        message: this.$t('global_images') + this.$t('global_show_msg_save_success')
      }
    },
    // 校验字段
    async validateFields() {
      // alt && desc 长度校验
      const validate = await this.validateCount()
      if (!validate) {
        return {
          valid: false,
          field: 'alt',
          message: ' '
        }
      }
      // 配置了 alt
      if (this.needAlt && this.needValidAlt && !this.disabledAlt && !this.validateAlt()) {
        return {
          valid: false,
          field: 'alt',
          message: 'Please input the alt of photo'
        }
      }
      // 配置了 desc
      if (this.needDesc && this.needValidDesc && !this.validateDesc()) {
        return {
          valid: false,
          field: 'desc',
          message: 'Please input the desc of photo'
        }
      }
      // 配置了 order
      if (this.needOrder && !this.validateOrder()) {
        return {
          valid: false,
          field: 'display_order',
          message: 'Duplicate display order'
        }
      }
      if (!this.validateName()) {
        return {
          valid: false,
          field: 'name',
          message: 'Empty Image'
        }
      }
      // 成功
      return {
        valid: true,
        field: '',
        message: this.$t('global_images') + this.$t('global_show_msg_save_success')
      }
    },
    // 校验文字长度
    async validateCount() {
      const { disabled, current_list } = this
      if (disabled) {
        return true
      }
      const list = current_list.map((item, index) => {
        const ref = this.$refs[`option${index}`]
        if (ref) {
          return ref.validateForm()
        }
        return true
      })
      const validate = await Promise.all(list)
      return validate.every((item) => item)
    },
    validateCountFun(word) {
      const { wordCountLimit } = this
      const str = word.trim()
      return str.length <= wordCountLimit
    },
    // 校验 alt
    validateAlt() {
      // 是否有空
      return this.current_list.every((photo) => photo.alt)
    },
    // 校验 desc
    validateDesc() {
      // 是否有空
      return this.current_list.every((photo) => photo.desc)
    },
    // 校验 desc
    validateOrder() {
      let arr = this.current_list.map((photo) => photo.display_order)
      let deduplicateArr = [...new Set(arr)]
      //! 去重之后如果长度不一致说明有重复数据
      return arr.length === deduplicateArr.length
    },
    // 获取配置
    getRefConfig(config) {
      if (!this.refLang) {
        return null
      }
      // if(config)
      let ref_list = (this.listAll.find((v) => v.language_type === this.refLang) || {}).list
      // 找到名称相同图片的作为参考
      if (ref_list && ref_list.length) {
        return ref_list.find((photo) => photo.display_order === config.display_order)
      }
      return null
    }
  }
}
</script>
<style lang="scss" scoped>
.act-picture-box {
  font-size: 14px;
  color: #212121;
  margin: 0 0 4px 0;
  &__title {
    line-height: 32px;
  }
}
</style>
<style lang="scss">
iframe[allow='camera'] {
  z-index: 1000 !important;
}
.submit-confirm-dialog-2 {
  .common-cb .ant-checkbox-wrapper.align {
    width: 195px;
    position: relative;
    margin-right: 12px;
    margin-left: 0;

    .ant-checkbox {
      display: none;
      position: absolute;
      top: 6px;
      right: 0;
      z-index: 10;

      + span {
        padding: 0;
      }
    }
  }

  .common-cb .ant-checkbox-wrapper-checked.align {
    .ant-checkbox {
      display: block;
    }

    div {
      img {
        opacity: 0.6;
      }
    }
  }

  .ant-checkbox-checked::after {
    border: none;
  }
}
.photo-group {
  .pic-upload-btn {
    margin-right: 12px;
  }

  .no-photo-tip {
  }

  .photo-group__upload {
    padding: 10px 0;
  }
  // animation
  .list-enter-active {
    transition: all 0.3s;
  }
  /** 移除过程 **/
  .list-leave-active {
    transition: all 0.3s;
  }
  /*** 开始插入、移除结束的位置变化 ***/
  .list-enter,
  .list-leave-to {
    max-height: 0;
    margin-bottom: 0;
    padding: 0 15px;
    // padding: 0;
    opacity: 0;
    transform: translateY(20px);
  }

  .__option-content {
    display: flex;
    align-items: center;
  }
}
</style>
