<template>
  <div
    v-if="!disabledRefFieldTagList.includes(option && option.ref_field_tag)"
    class="compound-option"
    :class="{
      [`is-${type}`]: true,
      'is-selected': chosen,
      'is-edit': editingFreeText,
      'not-saved': notSaved,
      'draft-is-deleted': draftIsDeleted
    }"
    :data-option-id="option ? option.id : ''"
    :data-position="datasetPosition"
    :data-ref_field_tag="(option && option.ref_field_tag) || ''"
  >
    <component
      :is="multi ? 'a-checkbox' : 'a-radio'"
      v-if="isText || isTemplate"
      :key="`option${option.id}`"
      :label="label"
      name="option"
      :disabled="disableCheckByCancellation || disabledCheckbox"
      class="compound-option__label dd"
      :data-ref_field_tag="option.ref_field_tag"
      :value="genUUID(option)"
      @click.stop="(evt) => handleCheck(evt.target.checked)"
    />
    <!-- text -->
    <div
      class="compound-option__content compose-relative"
      :class="{ 'compose-disable': isAshOrTrashData, 'is-editable': displayEditIcon }"
      @click.stop="handle2EditingState"
    >
      <div
        class="compound-option-box"
        :class="{
          'is-view-action-content': viewActionContent,
          'is-editing-content': isActivateEditing
        }"
      >
        <!-- 非 template 需整体覆盖 -->
        <a-tooltip v-if="!isTemplate && ((option && option.ash) || isTrashState)" placement="top">
          <template slot="title">
            <span>{{ $t('26083') }}</span>
          </template>
          <div class="over-view"></div>
        </a-tooltip>

        <DataPublishDotStyle
          v-if="
            hasDraftState && (draftIsDeleted || (!isActivateEditing && [0].includes(option_data.published)))
          "
          :published="draftIsDeleted ? 0 : option_data.published"
        />
        <div class="compound-option-content">
          <a-tooltip v-if="editingTemplate" placement="top" title="关闭编辑">
            <div class="close-editing-template" @click.stop="handleCloseEditing">
              <a-icon type="close" />
            </div>
          </a-tooltip>

          <!-- template 只需在内容区覆盖, 比较美观 -->
          <a-tooltip v-if="isTemplate && ((option && option.ash) || isTrashState)" placement="top">
            <template slot="title">
              <span>{{ $t('26083') }}</span>
            </template>
            <div class="over-view"></div>
          </a-tooltip>

          <div class="compound-option-text">
            <span v-show="computedShowOptionHtml" v-html="optionHtml"></span>
          </div>
          <div v-if="!isText" v-show="isActivateEditing" class="compound-option-edit">
            <template v-if="isTemplate">
              <compose
                v-if="chosen || Boolean(option.deleted && option.instance_id)"
                ref="composes"
                v-model="currentData"
                :ui_var_map="ui_var_map"
                :ref_data="ref_option_data"
                :field="field"
                :label="option.value"
                :chosen="chosen || Boolean(option.deleted && option.instance_id)"
                :option="option"
                :is-activate-editing="isActivateEditing"
                :readonly="readonly"
                :label_html.sync="label_html"
                :is-publish-with-ai="isPublishWithAi"
                v-bind="$attrs"
              />

              <div v-if="hasDraftState" class="__editing-btns">
                <label v-if="notSaved" class="prompt-save-tips">
                  {{ $t('82552') }}
                </label>
                <a-button size="small" type="default" @click.stop="handleResetItemData">
                  {{ $t('global_reset') }}
                </a-button>
                <a-button size="small" type="primary" @click.stop="handleSave2Draft">
                  {{ $t('77529') }}
                </a-button>
              </div>
            </template>

            <div v-else class="freetext-option">
              <template v-if="isAdd">
                <nobreak-textarea
                  v-if="isAdd"
                  ref="nobreaktextarea"
                  v-model.trim="addValue"
                  :disabled="readonly || isPublishWithAi"
                  autofocus
                  :class="injectComponentClassNameListByKey('input')"
                  :switch="switchNobreakTextarea"
                  :word-count-limit="wordCountLimit"
                  @keyup.enter.native="handleConfirmAddFreeText(switchNobreakTextarea)"
                />
              </template>

              <template v-else>
                <p v-if="isEN && !isENDraft">{{ option_data.value }}</p>
                <nobreak-textarea
                  v-else
                  ref="nobreaktextarea"
                  v-model.trim="edit_value"
                  autofocus
                  :disabled="readonly || isPublishWithAi"
                  :class="injectComponentClassNameListByKey('input')"
                  :switch="switchNobreakTextarea"
                  :word-count-limit="wordCountLimit"
                />
              </template>
              <a-input
                v-if="option_data && ref_lang"
                v-model.trim="ref_data.value"
                type="textarea"
                disabled
              />

              <!-- 关于 free text 的编辑操作 -->
              <div v-if="isAdd || (editingFreeText && !isPublishWithAi)" class="compound-option__btns">
                <a-space size="small">
                  <a-button
                    v-if="isAdd"
                    type="primary"
                    size="small"
                    :disabled="!addValue"
                    @click.stop="handleConfirmAddFreeText"
                  >
                    {{ hasDraftState ? $t('77529') : $t('global_button_save') }}
                  </a-button>

                  <a-button
                    v-else-if="editingFreeText"
                    type="primary"
                    size="small"
                    :disabled="!edit_value"
                    @click.stop="handleConfirmEditFreeText"
                  >
                    {{ hasDraftState ? $t('77529') : $t('global_button_save') }}
                  </a-button>

                  <a-button
                    v-if="!option_data || editingFreeText"
                    size="small"
                    @click.stop="handleCancelAddFreeText"
                  >
                    {{ $t('global_button_cancel') }}
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </div>

        <div v-if="chosen" class="compound-option__operation">
          <!-- delete -->
          <a-popconfirm
            :title="isFreeTextItem ? 'Are you sure to delete this freetext item?' : $t('js_confirm_delete')"
            placement="topRight"
            :align="{
              offset: [14, 0]
            }"
            @visibleChange="handleVisibleChange"
            @confirm="confirmDeleteItem"
          >
            <a-tooltip
              placement="left"
              :title="
                isFreeTextItem
                  ? isPriorLang
                    ? $t('delete_free_text_hint')
                    : $t('delete_free_text_disabled')
                  : $t('global_delete')
              "
            >
              <a-icon v-if="!readonly" type="delete" class="is-delete operation-btn" @click.stop></a-icon>
            </a-tooltip>
          </a-popconfirm>

          <!-- copy -->
          <a-tooltip
            v-if="
              !!(copyTimes > 0 && multi && !option.is_free_text && option.type !== 1) &&
              !isTrashData &&
              !isAshOrTrashData
            "
            placement="top"
            :title="$t('76544')"
          >
            <a-icon
              type="copy"
              class="group-action-btn copy operation-btn"
              size="small"
              :alt="$t('global_copy')"
              @click.stop="handleCopyItem"
            />
          </a-tooltip>

          <!-- 非激活编辑态 且 能编辑文本或是 template attr -->
          <a-icon
            v-if="displayEditIcon && !isAshOrTrashData"
            type="edit"
            class="operation-btn"
            @click.stop="handle2EditingState"
          />

          <!-- sort -->
          <a-icon v-if="allowSort" type="menu" class="is-sort operation-btn sort-btn" />

          <slot name="operation" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { genUUID } from '@activity/utils'
import { NobreakTextarea } from '../../../index'
import Compose from './Compose.vue'
const commonmark = require('commonmark')
import xssFilters from 'xss-filters'
import { ACTIVATE_EDITING_EVENT_KEY } from '@activity/pages/activityManagement/detailV2/utils'
import DataPublishDotStyle from '@activity/components/widgets/Attribute/src/DataPublishDotStyle'
import { KEY_MAP } from '~src/modules/activity/components/widgets/Attribute/constant'
import { imageDisplayHtml } from '~src/modules/activity/components/widgets/Attribute/constant'

// import DuplicateSvg from '../svg/duplicate-svg.vue'
/**
 * 属性值 - 即单选 or 多选选项
 * 当前属性值只可以复制, 不可以删除 or 编辑
 */

let typeStruct = ADMIN_API.struct.interface({
  id: 'number',
  order: 'number?', // TODO options can duplicate ?
  type: 'number?',
  value: 'string',
  variable_map: 'undefined|object'
})

export default {
  name: 'CompoundOption',
  inject: {
    ref_lang: 'ref_lang',
    edit_lang: 'edit_lang',
    locale_info: 'locale_info',
    injectComponentClassNameListByKey: 'injectComponentClassNameListByKey',
    updateText: 'updateText',
    extra_type: {
      default: 'default'
    }
  },
  componentName: 'CompoundOption',
  components: {
    NobreakTextarea,
    Compose,
    DataPublishDotStyle
  },
  props: {
    ref_data: Object,
    label: {
      type: String
    },
    chosen: {
      type: Boolean
    },
    // 顺序
    index: {
      type: Number
    },
    // 类型, 三种
    type: {
      type: String,
      default: 'text' // freetext or text or template or add
    },
    // 复杂类型配置
    option: {
      type: Object,
      default: () => {}
    },
    // 多选 or 单选
    multi: {
      type: Boolean,
      default: false
    },

    // cancellation_policy
    disableCheckByCancellation: {
      type: Boolean,
      default: false
    },
    option_data: {
      type: Object
    },
    // 复制次数
    copyTimes: {
      type: Number,
      default: 0
    },
    // 总数
    total: {
      type: Number,
      default: 0
    },
    readonly: {
      type: Boolean,
      default: false
    },
    closeNobreakTextarea: {
      type: Boolean,
      default: false
    },
    isPublishWithAi: {
      type: Boolean,
      default: false
    },
    field: {
      type: String,
      required: true
    },
    groupId: {
      type: Number,
      default: 0
    },
    titleId: {
      type: Number,
      default: 0
    },
    allowSort: {
      type: Boolean,
      default: false
    },
    hasDraftState: {
      type: Boolean,
      default: false
    },
    draftAttrValuesDeleted: {
      type: Array,
      default: () => []
    },
    // 当前 title 的值
    data: {
      type: Array,
      required: true
    },
    tempCloseExpandEditing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addValue: '',
      edit_value: (this.option || {}).value,
      label_html: {},
      editingTemplate: false,
      editingFreeText: false, // 区别于 isAdd free text 操作
      viewActionContent: false, // 因 pop-confirm 操作而失去焦点时，保留 hover 选中样式

      notSaved: false
      // tempData: []
    }
  },
  computed: {
    wordCountLimit() {
      return this.$attrs?.config?.word_count_limit ?? []
    },
    draftIsDeleted() {
      return (this.option && this.draftAttrValuesDeleted.includes(genUUID(this.option))) || false
    },
    isFreeTextItem() {
      return (
        (!this.isAdd && this.option.is_free_text && !this.editingFreeText && this.isPriorLang) ||
        this.isTrashState
      )
    },
    datasetPosition() {
      return this.option ? `${this.field}_${this.groupId}_${genUUID(this.option)}` : ''
    },
    optionHtml() {
      if (this.isText) {
        return this.option.value
      }

      if (this.isTemplate) {
        return this.showText
      }

      return this.isEN && !this.isENDraft ? this.option_data.value : this.edit_value
    },
    computedShowOptionHtml() {
      // 当v是图片时 且 是 isActivateEditing 时不显示
      const currDataType = this.currentData?.[this.edit_value]?.type
      return !(currDataType === KEY_MAP['PHOTO'] && this.isActivateEditing)
    },
    showText() {
      if (!this.option) {
        return ''
      }

      if (!this.isTemplate) {
        return this.option.value
      }

      let template = this.option.value
      this.ui_var_map.forEach((v) => {
        let isSelect = [1, 4, 5, 6, 7].includes(+v.type)
        let value = this.label_html[v.key]

        let className = isSelect ? 'variable-select' : 'variable-input'
        className += value ? ' variable-complete' : ' variable-undone'

        const isPhoto = v.type === KEY_MAP['PHOTO']
        const innerText =
          +v.type === 2 && value
            ? this.fmtMarkdown(value, true)
            : isPhoto && !Object.keys(this.currentData)?.length
            ? imageDisplayHtml
            : value || v.key
        const el = `<span class="variable ${className}"> ${innerText} </span>`

        template = template.replace(v.key, el)
      })

      return template
    },
    // 是否激活了编辑
    isActivateEditing() {
      return this.editingFreeText || this.isAdd || this.editingTemplate
    },
    isTrashData() {
      return this.option?.isTrashData ?? false
    },
    // 新增 free text 时 option_data 为空
    isTrashState() {
      return this.isTrashData && !_.isEmpty(this.option_data)
    },
    disabledCheckbox() {
      // 因为废弃 title 而导致的虚增的选项，有值则可以取消勾选，无值则不可再选中
      if (this.option.isInflate || this.isTrashState) {
        return !this.chosen
      }

      return this.option.is_free_text || this.readonly || this.option.ash
    },
    switchNobreakTextarea() {
      return !this.closeNobreakTextarea
    },
    // todo 待确定要删除
    disabledRefFieldTagList() {
      return this.$attrs.disabledRefFieldTagList || []
    },
    isEN() {
      return this.edit_lang == 'en_US'
    },
    isPriorLang() {
      return this.isOnlyLang || this.isEN
    },
    isOnlyLang() {
      return (this.locale_info?.react_data || []).filter((v) => v.status)?.length == 1
    },
    isENDraft() {
      let langStatus = { preview: 2 }

      return (
        _.get(
          this.locale_info?.react_data?.find?.((v) => v.language == 'en_US'),
          'status',
          0
        ) <= langStatus.preview
      )
    },
    ref_option_data() {
      return this.ref_data.variable_map || {}
    },
    currentData: {
      get() {
        return this.option_data?.variable_map || {}
      },
      set(v) {
        let result = typeStruct({
          ..._.cloneDeep(this.option),
          variable_map: v
        })
        this.$emit('change', result)
      }
    },
    // 存在变量的 attr tpl
    hasVariableAttr() {
      return this.option.type === 2
    },
    ui_var_map() {
      if (this.hasVariableAttr) {
        let template_str = this.option.value
        let var_arr = template_str.match(/{{.+?}}/g) || []
        let sortted_keys = _.sortBy(Object.keys(this.option.variable_map), (v) =>
          var_arr.findIndex((vv) => vv == v)
        )

        return sortted_keys.map((k) => this.option.variable_map[k])
      } else {
        return []
      }
    },
    isAdd() {
      return this.type === 'add'
    },
    // template text
    isText() {
      return this.type !== 'add' && !this.option.is_free_text && _.isEmpty(this.option.variable_map)
    },
    // template variable
    isTemplate() {
      return this.type !== 'add' && !_.isEmpty(this.option.variable_map)
    },
    // 脏数据或者是对应属性已被删的无效数据
    isAshOrTrashData() {
      return (this.option && this.option.ash) || this.isTrashState
    },
    // 能被编辑的 free text
    editableFreeText() {
      return !(this.isText || this.isTemplate || (this.isEN && !this.isENDraft)) && !this.isAshOrTrashData
    },
    editableTemplate() {
      return this.isTemplate && this.chosen
    },
    displayEditIcon() {
      return !this.isActivateEditing && (this.editableFreeText || this.editableTemplate)
    },
    // 非默认且有草稿态
    hasValidPublishDraftState() {
      return this.hasDraftState && this.option.published !== -1
    },
    refFieldTag() {
      return this.option?.ref_field_tag ?? ''
    }
  },
  watch: {
    editingTemplate(val) {
      if (this.chosen) {
        if (val) {
          !this.notSaved && this.settingCache(this.option_data)
        } else if (this.cacheEditTplData && !_.isEqual(this.cacheEditTplData, this.option_data)) {
          this.$emit('reset', { option: this.cacheEditTplData, displayMsg: false })
        }
      } else {
        this.settingCache(null)
      }
    },
    chosen: {
      handler(v) {
        if (v) {
          if (this.editableTemplate) {
            if (this.tempCloseExpandEditing) {
              // Temporarily close the expand edit
              this.$emit('cancelCloseExpandEditing')
            } else {
              // 自动展开
              this.editingTemplate = true
            }
          }
          // 如果是脏数据，重新勾选之后可以正名
          if (this.option.isTrashData) {
            this.option.isTrashData = false
          }
        } else {
          this.editingTemplate = false
          this.notSaved = false
        }
      }
    },
    showText: {
      immediate: true,
      handler(next) {
        // 新增 free text 无 option data
        if (!this.option) {
          return
        }

        let obj = {
          label: this.option.value,
          data_var_map: {},
          _html: next
        }
        // for (let key in this.data_var_map) {\
        // (this.data_var_map[v.key].values || [])
        this.ui_var_map.forEach((v) => {
          obj.data_var_map[v.key] = {
            type: v.type,
            value: (_.get(this.data_var_map, `${v.key}.values`) || []).map((v) => v.value)
          }
        })
        // }
        this.updateText(this.option, obj)
      }
    },
    edit_value(next, old) {
      if (next === this.option_data.value) {
        this.editingFreeText = false
      } else {
        this.editingFreeText = true
      }
    }
  },
  mounted() {
    klook.bus
      .$on(ACTIVATE_EDITING_EVENT_KEY.activate, this.activateFn)
      .$on(ACTIVATE_EDITING_EVENT_KEY.close, this.closeFn)

    if (this.refFieldTag) {
      klook.bus.$off(this.refFieldTag).$on(this.refFieldTag, (data) => {
        const { action } = data

        if (action === 'autoSelect') {
          this.handleCheck(true)
          this.$emit('autoSelectOption', { value: genUUID(this.option) })
        }
      })
    }
  },
  beforeDestroy() {
    this.refFieldTag && klook.bus.$off(this.refFieldTag)

    klook.bus
      .$off(ACTIVATE_EDITING_EVENT_KEY.activate, this.activateFn)
      .$off(ACTIVATE_EDITING_EVENT_KEY.close, this.closeFn)
  },
  methods: {
    genUUID,
    activateFn() {
      this.handle2EditingState()
    },
    closeFn() {
      this.editingFreeText = false
      this.editingTemplate = false
    },
    settingCache(data) {
      this.$nextTick(() => {
        this.cacheEditTplData = _.cloneDeep(data)
      })
    },
    tplDataNotSaved() {
      this.notSaved =
        this.isTemplate && this.cacheEditTplData && !_.isEqual(this.cacheEditTplData, this.option_data)

      if (this.notSaved) {
        this.handle2EditingState()
      }

      return this.notSaved
    },
    async validate({ checkSave2Draft = true } = {}) {
      let composes = this.$refs.composes
      let valid = true
      if (composes && composes.chosen) {
        valid = await composes.validate()
      }

      if (!valid) {
        this.handle2EditingState()

        return false
      }

      if (
        !valid ||
        // 需要检查草稿且有勾选
        (checkSave2Draft && this.hasDraftState && this.chosen && ![0, 1].includes(this.option.published))
      ) {
        this.handle2EditingState()
        // 没有保存为草稿
        if (valid) {
          this.notSaved = true

          return false
        }
      }

      return valid
    },
    // 通过点击去跟踪草稿状态
    async handleCheck(checked) {
      if (this.hasDraftState) {
        if (checked) {
          this.selectOption2Draft()
        } else if (this.option_data.published !== -1) {
          this.unselectOption2Draft()
        }
      }
    },
    handleCloseEditing() {
      this.editingTemplate = false
      this.operationProtection = true
      setTimeout(() => {
        this.operationProtection = false
      }, 100)
    },
    selectOption2Draft() {
      if (this.hasDraftState) {
        // 有变量的 tpl 只有点击了保存才会变为 published 0 草稿态
        if (this.hasVariableAttr) {
          if (this.option.deleted && this.option.instance_id) {
            Object.assign(this.option, {
              deleted: false,
              published: -1
            })
            this.$emit('deleteInvalidOptionData', { option: this.option })
          }
          this.$emit('changePublishedValue', { index: this.index, published: -1 })
        } else {
          // 无变量 tpl 直接变成草稿
          this.$emit('saveOption2Draft', {
            option: { ...this.option, published: -1 }, // -1 是 insert
            index: this.index,
            published: 0
          })
        }
      }
    },
    unselectOption2Draft() {
      if (this.hasValidPublishDraftState) {
        this.$emit('unselectOption2Draft', { option: this.option_data, label: this.label })
      }
    },
    handleResetItemData() {
      const option = this.getEmptyItemData()

      if (this.hasValidPublishDraftState) {
        this.$emit('resetOption2Draft', { option, index: this.index })
      } else {
        this.$emit('reset', { option })
      }

      this.settingCache(option)
    },
    async handleSave2Draft({ published = 0 } = {}) {
      if (this.hasDraftState) {
        const valid = await this.validate({ checkSave2Draft: false })
        if (!valid) {
          return
        }

        this.settingCache(this.option_data)
        this.editingTemplate = false
        this.$emit('saveOption2Draft', {
          option: this.hasVariableAttr ? this.option_data : this.option,
          index: this.index,
          published
        })
      }

      this.notSaved = false
    },
    handle2EditingState() {
      if (this.editableFreeText) {
        this.editingFreeText = true
      } else if (this.editableTemplate) {
        this.editingTemplate = true
      }
    },
    handleUnselect() {
      if (this.operationProtection) return

      // 纯文本tpl 或者 带变量的tpl
      if (this.isText || this.isTemplate) {
        if (this.hasDraftState) {
          this.unselectOption2Draft()
        } else {
          this.$emit('unselect', this.label)
        }
      } else {
        this.handleRemoveFreeText()
      }
    },
    handleVisibleChange(val) {
      this.viewActionContent = val
    },
    fmtMarkdown(value, inline = false) {
      if (!this.commonmark) {
        this.commonmark = commonmark
      }
      let reader = new this.commonmark.Parser()
      let writer = new this.commonmark.HtmlRenderer()

      value = value.replace(/javascript:/g, encodeURIComponent('javascript:'))

      let parsed = reader.parse(xssFilters.inHTMLData(value)) // parsed is a 'Node' tree
      let html = writer.render(parsed)

      if (inline) {
        return html.replace(/(<p)(\s*.*>\s*.*<\/)(p>)/g, '<p style="display: inline;"' + '$2' + 'p>')
      }
      return html
    },
    getEmptyItemData() {
      let data = _.cloneDeep(this.option_data)
      if (!_.isEmpty(data.variable_map)) {
        Object.keys(data.variable_map).forEach((key) => {
          data.variable_map[key].values = []
        })
      }
      data.published = -1

      return data
    },
    handleCopyItem() {
      if (this.operationProtection) return

      const option = this.getEmptyItemData()

      this.$emit('copyItem', {
        ...option,
        instance_id: '',
        isFromCopy: true
      })
    },
    confirmDeleteItem() {
      if (this.isFreeTextItem) {
        this.handleRemoveFreeText()
      } else {
        this.handleUnselect()
      }
    },
    handleRemoveFreeText() {
      if (this.operationProtection) return

      if (this.hasDraftState) {
        this.$emit('removeFreeText2Draft', { option: this.option })
      } else {
        this.$emit('removeFreeText', this.option)
      }
    },
    async handleConfirmEditFreeText() {
      const nobreaktextarea = this.$refs.nobreaktextarea
      if (nobreaktextarea) {
        const validate = await nobreaktextarea.validateForm()
        if (!validate) {
          return
        }
      }
      const option = {
        ...this.option_data,
        value: this.edit_value
      }

      if (this.hasDraftState) {
        const published = 0
        option.published = published
        this.$emit('updateFreeText2Draft', { option })
        this.$emit('changePublishedValue', { index: this.index, published: 0 })
      } else {
        this.$emit('confirm-edit', option)
      }

      this.editingFreeText = false
    },
    handleConfirmAddFreeText() {
      if (!this.addValue) {
        return
      }

      let option
      if (_.isEmpty(this.option_data)) {
        option = {
          create: true,
          is_free_text: true,
          value: this.addValue
        }
      } else {
        option = {
          ...this.option_data,
          value: this.addValue
        }
      }

      if (this.hasDraftState) {
        option.published = -1
        this.$emit('addFreeText2Draft', { option })
      } else {
        this.$emit('confirm-add', option)
      }

      this.editingFreeText = false
    },
    handleCancelAddFreeText() {
      if (this.editingFreeText) {
        this.edit_value = (this.option_data || {}).value
      }
      this.$emit('cancel-add')
      this.editingFreeText = false
    }
  }
}
</script>

<style lang="scss">
@use 'sass:color';
// 选项边框
$optionBorderColor: #d0e6fe;
// 内容lineheight
$contentHeight: 25px;
// 操作按钮宽度
$handlerBtnWidth: 60px;
// 操作按钮颜色
$handlerBtnColor: #107afbb7;

$borderRadius: 4px;

/*.compound-option__label {*/
/*align-self: start;*/
/*}*/
// 外层
.compound-select .compound-option {
  display: flex;
  width: 100%;
  min-width: 520px;
  align-items: center;
  border-radius: $borderRadius;
  cursor: pointer;

  .compose-relative {
    position: relative;
  }
  .over-view {
    background-color: rgba(34, 34, 34, 0.1);
    display: block;
    z-index: 100;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: not-allowed;
  }

  .compound-option-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    border: 1px solid transparent;
    border-radius: $borderRadius;

    &.is-view-action-content,
    &:hover {
      background-color: #ecf3fc;
    }

    &.is-editing-content {
      padding-left: 0;
      padding-right: 0;
      background-color: transparent !important;
      .compound-option-content {
        padding: 20px;
        border-color: #e0e0e0;
        background-color: #fff;
      }
      // 编辑状态需要显示操作按钮
      .compound-option__operation {
        visibility: inherit;
      }
    }

    &.is-view-action-content .compound-option__operation,
    &:hover .compound-option__operation {
      visibility: inherit;
    }

    .compound-option-content {
      position: relative;
      flex: 1;
      border: 1px solid transparent;
      border-radius: 4px;
      overflow: hidden;
    }

    .close-editing-template {
      position: absolute;
      top: -5px;
      right: -5px;
      width: 24px;
      height: 24px;
      font-size: 10px;
      color: rgba(0, 0, 0, 0.45);
      border-radius: 50%;
      text-align: center;
      line-height: 24px;
      user-select: none;
      cursor: pointer;
      z-index: 1001;

      &:hover {
        color: rgba(0, 0, 0, 0.8);
        background-color: #f0f7ff;
      }

      ::v-deep .anticon {
        position: relative;
        top: 2px;
        left: -2px;
      }
    }

    .compound-option__operation {
      display: inline-flex;
      align-items: center;
      justify-content: flex-end;
      visibility: hidden;
      margin-left: 16px;
      z-index: 999;

      .operation-btn {
        display: inline-block;
        margin-right: 16px;
        color: #437dff;

        &:last-child {
          margin-right: 0;
        }

        &.is-sort {
          color: #757575;
          cursor: grab;
        }

        &.is-delete {
          padding: 0;
          color: #ff4d4f;
        }
      }
    }
  }

  .compound-option-edit {
    .__editing-btns {
      margin-top: 24px;
      text-align: right;

      .ant-btn {
        margin-left: 8px;
      }

      .prompt-save-tips {
        color: #ff4d4f;
      }
    }
  }

  // 内容
  .compound-option__label {
    margin-right: 12px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 20px;
    .el-radio__label,
    .ant-radio + span,
    .el-checkbox__label,
    .ant-checkbox + span {
      display: none;
    }
  }
  // 内容
  .ant-radio-input {
    margin-top: 5px;
  }
  .compound-option__content {
    // padding: 8px;
    /*margin-left: 4px;*/
    // background-color: #fafafa;
    border-radius: $borderRadius;
    // border: 1px dashed transparent;
    transition: all 0.6s;
    color: #000;
    font-size: 14px;
    cursor: default;

    &.is-editable {
      cursor: pointer;
    }
  }
  // &:hover .compound-option__content {
  //   border-color: #ccc;
  // }
  // 添加输入框撑满
  &.is-add {
    display: block;
    overflow: hidden;
    margin-top: 12px;

    .compound-option__content {
      width: 100%;
    }
  }
  &.is-edit {
    display: block !important;
    overflow: hidden !important;

    .compound-option__content {
      width: 100%;
    }
  }

  // 文字
  &__content {
    // 内容
    display: inline-block;
    vertical-align: middle;
    width: 100%;
    // 处理文字长度超出
    word-break: break-word;
    p {
      line-height: $contentHeight;
      min-height: $contentHeight;
      white-space: normal;
      word-break: break-all;
      margin: 0;
      font-size: 13px;
    }
  }

  // 操作按钮样式
  &__icons {
    display: inline-block;
    margin-left: 12px;
    vertical-align: middle;
    opacity: 0;
    text-align: center;
    position: relative;
    left: 3px;
    transition: all 0.2s;
    color: $handlerBtnColor;
    cursor: pointer;
    font-size: 15px;
    font-weight: 400;
  }
  // 覆盖
  .compound-option__icons {
    line-height: 1;
    opacity: 1;
    left: 0;
    transition: all 0.3s;
    & > i:hover {
      cursor: pointer;
      color: color.scale($handlerBtnColor, $lightness: 10%);
    }
    & > i.el-icon-delete {
      &:hover {
        color: rgba(255, 0, 0, 0.555);
      }
    }
  }
  // 保存按钮
  &__btns {
    margin-top: 10px;
    float: right;
    display: inline-block;
  }
  .freetext-option {
    display: flex;
    flex-direction: column;
    .ant-input {
      width: 100%;
      margin-top: 10px;
    }
    .ant-input + .ant-input,
    .ant-input + textarea.ant-input {
      margin-top: 10px;
    }
    ::v-deep textarea.ant-input {
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  &.is-selected {
    .variable {
      color: #27aa7a;
    }
    .variable-complete {
      color: #16aa77;
      &.variable-input {
        background-color: rgba(198, 241, 221, 0.3);
      }
    }
    .variable-undone {
      color: #e64340;
      &.variable-input {
        background-color: rgba(255, 210, 210, 0.3);
      }
    }
    .ant-form-item-children {
      width: 100%;
      .ant-select,
      .ant-input {
        max-width: inherit;
      }
    }

    .variable-complete {
      color: #16aa77;
      &.variable-input {
        background-color: rgba(198, 241, 221, 0.3);
      }
    }
  }
}

.compound-option-text {
  line-height: 28px;
}
</style>
